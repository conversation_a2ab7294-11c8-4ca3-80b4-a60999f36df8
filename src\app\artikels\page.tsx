import Navigation from "../../components/Navigation";
import Footer from "../../components/Footer";
import Image from "next/image";
import Link from "next/link";
import { artikelsArticles, getArticlesByCategory, getAllCategories } from "../../data/artikelsArticles";

export const metadata = {
  title: "Artikels - Dierenverzekering Informatie | ZoekDierenverzekering",
  description: "Uitgebreide artikels over dierenverzekeringen en huisdierenzorg. Samengesteld met hulp van specialisten en experts.",
  keywords: [
    "dierenverzekering informatie",
    "artikels dierenverzekering", 
    "huisdierenzorg",
    "dierenverzekering gids",
    "dierenarts informatie",
    "huisdier verzorging",
    "dierenverzekering tips"
  ],
  alternates: {
    canonical: "https://zoekdierenverzekering.nl/artikels",
  },
  openGraph: {
    title: "Artikels - Dierenverzekering Informatie | ZoekDierenverzekering",
    description: "Uitgebreide artikels over dierenverzekeringen en huisdierenzorg. Samengesteld met hulp van specialisten en experts.",
    url: "https://zoekdierenverzekering.nl/artikels",
    siteName: "ZoekDierenverzekering.nl",
    locale: "nl_NL",
    type: "website"
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function ArtikelsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-tea_green-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 xl:gap-20 items-start lg:items-center">
            <div className="lg:pr-8 xl:pr-12">
              <h1 className="text-4xl sm:text-5xl lg:text-5xl xl:text-6xl font-bold text-celadon-100 mb-6">
                Artikels
              </h1>
              <p className="text-xl text-gray-600 mb-4 max-w-lg">
                Uitgebreide artikels over dierenverzekeringen en huisdierenzorg.
              </p>
              <p className="text-lg text-gray-600 max-w-lg">
                Samengesteld met hulp van specialisten en experts.
              </p>
            </div>

            <div className="relative lg:pl-8 xl:pl-12">
              <div className="bg-orange-200 rounded-full w-80 h-80 xl:w-96 xl:h-96 mx-auto flex items-center justify-center">
                <Image
                  src="/images/hero.webp"
                  alt="Dierenarts met hond"
                  width={300}
                  height={300}
                  className="rounded-full object-cover xl:w-[350px] xl:h-[350px]"
                />
              </div>
              {/* Paw decoration */}
              <div className="absolute top-4 right-4 text-orange-300 text-4xl">
                🐾
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Articles */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#2F2E51] text-center mb-12">
            Nieuwe artikelen
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {artikelsArticles
              .sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime())
              .slice(0, 3).map((article) => (
              <Link
                key={article.id}
                href={`/artikels/${article.slug}`}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-6">
                  <div className="text-sm text-celeste-300 font-medium mb-2">
                    {article.category}
                  </div>
                  <h3 className="text-lg font-bold text-celadon-100 mb-2 hover:text-celeste-300 transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {article.description}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Knowledge Categories */}
      <section className="py-16 bg-tea_green-800">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          
          {/* Dynamic Categories */}
          {getAllCategories().map((category) => {
            const categoryArticles = getArticlesByCategory(category);
            
            return (
              <div key={category} className="mb-16">
                <h2 className="text-3xl font-bold text-celadon-100 mb-8">{category}</h2>
                
                {/* Card-based layout for better readability */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {categoryArticles.map((article) => (
                    <Link
                      key={article.id}
                      href={`/artikels/${article.slug}`}
                      className="block bg-white/90 backdrop-blur-sm rounded-lg p-6 shadow-md hover:shadow-xl hover:bg-white transition-all duration-300 hover:-translate-y-1 group"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-[#2F2E51] group-hover:text-celeste-300 line-clamp-2 transition-colors">
                            {article.title}
                          </h3>
                        </div>
                        <svg 
                          className="w-5 h-5 text-celeste-300 group-hover:text-celeste-400 ml-3 flex-shrink-0 transition-transform group-hover:translate-x-1" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                      
                      <p className="text-gray-600 text-sm line-clamp-3 mb-4">
                        {article.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-celeste-300 bg-celeste-50 px-2 py-1 rounded-full">
                          {article.category}
                        </span>
                        <span className="text-xs text-gray-500">
                          Lees meer →
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            );
          })}
          
        </div>
      </section>

      <Footer />
    </div>
  );
}
