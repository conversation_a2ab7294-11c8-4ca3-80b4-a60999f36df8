"use client"

import { motion } from 'framer-motion'
import Image from 'next/image'

export default function TrustSection() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-[#FFF5ED]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2F2E51] mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Waarom kiezen voor ZoekDierenverzekering.nl
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Onze vergelijkingsservice helpt je bij het vinden van de juiste dierenverzekering 
            door transparante informatie over Nederlandse verzekeraars.
          </motion.p>
        </motion.div>

        {/* Trust Features */}
        <div className="grid sm:grid-cols-3 gap-8 mb-16">
          {/* Feature 1 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">2</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Verzekeraars Vergeleken</h3>
            <p className="text-gray-600">
              We vergelijken momenteel Figo en OHRA dierenverzekeringen voor transparante keuzes
            </p>
          </motion.div>

          {/* Feature 2 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">✓</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Gratis Vergelijken</h3>
            <p className="text-gray-600">
              Onze vergelijkingsservice is altijd gratis en zonder verplichtingen
            </p>
          </motion.div>

          {/* Feature 3 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0, ease: "easeOut" }}
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.div
              className="w-24 h-24 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4"
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.3 }}
            >
              <span className="text-2xl font-bold text-white">NL</span>
            </motion.div>
            <h3 className="text-xl font-bold text-[#2F2E51] mb-2">Nederlandse Focus</h3>
            <p className="text-gray-600">
              Specialist in Nederlandse dierenverzekeringen met actuele informatie
            </p>
          </motion.div>
        </div>

        {/* Why Choose Us Section */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8 lg:p-12 mb-16">
          <h3 className="text-2xl sm:text-3xl font-bold text-center text-[#2F2E51] mb-8">
            Daarom kies je voor een dierenverzekering
          </h3>
          
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left content */}
            <div>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                Een huisdier brengt zorgkosten met zich mee. Iedere hond of kat moet wel eens naar de dokter, 
                net als jijzelf. Voorkom financiële verrassingen bij de dierenarts en sluit een passende 
                dierenverzekering af voor je trouwe dierenvriend.
              </p>

              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Vergelijk meerdere Nederlandse verzekeraars (Figo en OHRA)</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Transparante voorwaarden en dekkingsoverzicht</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Gratis vergelijkingsservice</span>
                </li>
                <li className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-gray-700 font-medium">Actuele tarieven en informatie</span>
                </li>
              </ul>
            </div>

            {/* Right content - Image */}
            <div className="relative">
              <div className="w-full h-80 rounded-xl shadow-lg overflow-hidden">
                <Image 
                  src="/images/man-about-to-kiss-a-cute-puppy-dog-outdoors-2025-03-07-23-33-25-utc.webp" 
                  alt="Man about to kiss a cute puppy"
                  className="w-full h-full object-cover"
                  width={400}
                  height={320}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Smart Comparison Section */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left side */}
          <div>
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2F2E51] mb-6">
              Slim vergelijken op ZoekDierenverzekering.nl
            </h3>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Bekijk, vergelijk en kies in een paar klikken de perfecte verzekering voor jouw viervoeter. 
              Onze vergelijker toont alle Nederlandse dierenverzekeraars met actuele premies en voorwaarden.
            </p>

            <div className="space-y-6">
              {/* Feature 1 */}
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-bold text-[#2F2E51] mb-2">Actuele en correcte informatie</h4>
                  <p className="text-gray-600">
                    Je huisdier is een belangrijk onderdeel van je gezin. Samen met onze experts zorgen we 
                    ervoor dat alle verzekeringsvoorwaarden, premies en andere gegevens actueel zijn.
                  </p>
                </div>
              </div>

              {/* Feature 2 */}
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-bold text-[#2F2E51] mb-2">Transparant vergelijken</h4>
                  <p className="text-gray-600">
                    ZoekDierenverzekering.nl is een vergelijkingsplatform dat momenteel
                    Figo en OHRA dierenverzekeringen vergelijkt. We ontvangen commissie bij afsluiting.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Platform Stats */}
          <div className="bg-gradient-to-br from-[#2F2E51] to-slate-700 rounded-2xl p-8 text-white">
            <h4 className="text-2xl font-bold mb-6">Waarom ZoekDierenverzekering.nl?</h4>

            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl font-bold">7+</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Verzekeraars</div>
                  <div className="text-blue-100 text-sm">Alle Nederlandse aanbieders</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl font-bold">✓</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Transparant</div>
                  <div className="text-blue-100 text-sm">Geen verborgen kosten</div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xl font-bold">€11</span>
                </div>
                <div>
                  <div className="text-white font-semibold">Vanaf per maand</div>
                  <div className="text-blue-100 text-sm">Katten vanaf €11 p/m*</div>
                </div>
              </div>
            </div>

            {/* Info in stats box */}
            <div className="mt-8 p-4 bg-white/10 rounded-xl border border-white/20">
              <div className="text-sm text-blue-100 mb-2">Actuele tarieven</div>
              <div className="text-2xl font-bold text-white mb-2">Augustus 2025</div>
              <div className="text-sm text-blue-100">Laatst bijgewerkt</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}