"use client"

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ResponsiveReviewModal from "./ResponsiveReviewModal";
import ModernComparisonBenefits from "./ModernComparisonBenefits";
import ModernConclusionSection from "./ModernConclusionSection";

interface CoverageItem {
  name: string;
  covered: boolean;
  percentage?: string;
}

interface InsuranceCompany {
  name: string;
  rating: number;
  reviewCount: number;
  features: string[];
  highlighted?: boolean;
  highlightText?: string;
  logo: string;
  dogPrice: string;
  catPrice: string;
  maxCoverage: string;
  ownRisk: string;
  monthlyFee: string;
  coverage: {
    covered: CoverageItem[];
    notCovered: CoverageItem[];
  };
}

const insuranceCompanies: InsuranceCompany[] = [
  {
    name: "Figo",
    rating: 7.9,
    reviewCount: 41,
    features: [
      "Alle rassen en leeftijden welkom"
    ],
    highlighted: true,
    highlightText: "Meest uitgebreide dekking",
    logo: "FIGO",
    dogPrice: "17,00",
    catPrice: "11,00",
    maxCoverage: "€ 3.000,00",
    ownRisk: "Geen",
    monthlyFee: "€ 0,00",
    coverage: {
      covered: [
        { name: "Consult dierenarts", covered: true, percentage: "50%" },
        { name: "Diergeneesmiddelen", covered: true, percentage: "50%" },
        { name: "Operaties", covered: true, percentage: "50%" },
        { name: "Euthanasie", covered: true, percentage: "50%" },
        { name: "Echo- en röntgenonderzoek", covered: true, percentage: "50%" },
        { name: "Identificatiechip", covered: true, percentage: "50%" },
        { name: "Opname/ verblijf kliniek", covered: true, percentage: "50%" },
        { name: "Aandoeningen gewrichten", covered: true, percentage: "50%" },
        { name: "Vaccinaties", covered: true, percentage: "50%" }
      ],
      notCovered: [
        { name: "Castratie/ sterilisatie", covered: false },
        { name: "Dekking buitenland", covered: false }
      ]
    }
  },
  {
    name: "PetSecur",
    rating: 7.9,
    reviewCount: 70,
    features: [
      "Declaratie behandeld binnen 5 werkdagen",
      "Nu met verbeterde dekking!"
    ],
    logo: "PETSECUR",
    dogPrice: "18,47",
    catPrice: "16,28",
    maxCoverage: "€ 12.500,00",
    ownRisk: "Geen",
    monthlyFee: "€ 0,00",
    coverage: {
      covered: [
        { name: "Consult dierenarts", covered: true, percentage: "50%" },
        { name: "Diergeneesmiddelen", covered: true, percentage: "50%" },
        { name: "Operaties", covered: true, percentage: "50%" },
        { name: "Euthanasie", covered: true, percentage: "50%" },
        { name: "Echo- en röntgenonderzoek", covered: true, percentage: "50%" },
        { name: "Castratie/ sterilisatie", covered: true, percentage: "50%" },
        { name: "Dekking buitenland", covered: true, percentage: "50%" },
        { name: "Laboratorisch onderzoek", covered: true, percentage: "50%" },
        { name: "Advertentie bij vermissing", covered: true, percentage: "50%" }
      ],
      notCovered: [
        { name: "Identificatiechip", covered: false },
        { name: "Vaccinaties", covered: false }
      ]
    }
  },
  {
    name: "OHRA",
    rating: 8.3,
    reviewCount: 62,
    features: [
      "Basispakket met hoogste vergoeding",
      "Beste afhandeling van declaraties"
    ],
    logo: "OHRA",
    dogPrice: "17,00",
    catPrice: "13,00",
    maxCoverage: "€ 2.500,00",
    ownRisk: "Geen",
    monthlyFee: "€ 0,00",
    coverage: {
      covered: [
        { name: "Consult dierenarts", covered: true, percentage: "80%" },
        { name: "Diergeneesmiddelen", covered: true, percentage: "80%" },
        { name: "Operaties", covered: true, percentage: "80%" },
        { name: "Euthanasie", covered: true, percentage: "80%" },
        { name: "Echo- en röntgenonderzoek", covered: true, percentage: "80%" },
        { name: "Identificatiechip", covered: true, percentage: "80%" },
        { name: "Opname/ verblijf kliniek", covered: true, percentage: "80%" },
        { name: "Aandoeningen gewrichten", covered: true, percentage: "80%" },
        { name: "Vaccinaties", covered: true, percentage: "80%" }
      ],
      notCovered: [
        { name: "Castratie/ sterilisatie", covered: false },
        { name: "Dekking buitenland", covered: false }
      ]
    }
  }
];

function InsuranceCard({ company, isHomepage = false }: { company: InsuranceCompany, isHomepage?: boolean }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showReviews, setShowReviews] = useState(false)

  const getPercentage = () => {
    if (company.logo === 'FIGO') return '50%'
    if (company.logo === 'PETSECUR') return '50%'
    if (company.logo === 'OHRA') return '80%'
    return '50%'
  }

  const getFigoAffiliateLink = () => {
    // Get current page path to determine appropriate affiliate link
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      if (path.includes('hondenverzekering')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fhondenverzekering%2F';
      } else if (path.includes('kattenverzekering')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fkattenverzekering%2F';
      } else if (path.includes('konijn')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fkonijnenverzekering%2F';
      } else if (path.includes('papegaai')) {
        return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2Fpapegaaienverzekering%2F';
      }
    }
    // Default to general homepage link
    return 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued=https%3A%2F%2Ffigopet.nl%2F';
  }

  const getPetSecurAffiliateLink = () => {
    // PetSecur affiliate link from affiliate-links.md
    return 'https://fr135.net/c/?si=15431&li=1668064&wi=411525&ws=zoekdierenverzekering&dl=%2F';
  }

  const getInsuranceLink = () => {
    if (company.name === 'Figo') {
      return getFigoAffiliateLink();
    }
    if (company.name === 'PetSecur') {
      return getPetSecurAffiliateLink();
    }
    // For OHRA and other companies, return placeholder for now
    return '#';
  }

  // New Figma-based design
  return (
    <div className={`relative bg-white rounded-[10px] border overflow-hidden shadow-sm ${
      company.highlighted
        ? 'border-[#4DC166] shadow-lg'
        : 'border-[#E5E5E5]'
    }`}>

      {/* Best Beoordeeld Badge for Figo */}
      {company.highlighted && (
        <div className="absolute top-0 right-0 bg-[#4DC166] text-white px-2 py-1 sm:px-4 sm:py-2 rounded-bl-lg text-xs sm:text-sm font-bold z-10">
          <span className="hidden sm:inline">★ ★ Best beoordeeld ★ ★</span>
          <span className="sm:hidden">★ Best ★</span>
        </div>
      )}

      {/* Desktop Layout */}
      <div className="hidden md:block">
        <div className="flex items-stretch">
          {/* Left Section - Company Info */}
          <div className="flex-1 p-6">
            {/* Logo and Company Name */}
            <div className="flex items-center gap-4 mb-4">
              <div className="w-24 h-16 flex items-center justify-center">
                {company.logo === 'FIGO' && (
                  <Image
                    src="/images/logos/figo-logo.svg"
                    alt="Figo Logo"
                    width={96}
                    height={64}
                    className="max-w-full max-h-full object-contain"
                  />
                )}
                {company.logo === 'OHRA' && (
                  <Image
                    src="/images/logos/ohra-logo.svg"
                    alt="OHRA Logo"
                    width={96}
                    height={64}
                    className="max-w-full max-h-full object-contain"
                  />
                )}
                {company.logo === 'PETSECUR' && (
                  <Image
                    src="/images/logos/petsecur-logo.png"
                    alt="PetSecur Logo"
                    width={96}
                    height={64}
                    className="max-w-full max-h-full object-contain"
                  />
                )}
                {company.logo === 'UNIVE' && (
                  <Image
                    src="/images/logos/unive-logo.svg"
                    alt="Univé Logo"
                    width={96}
                    height={64}
                    className="max-w-full max-h-full object-contain"
                  />
                )}
                {!['FIGO', 'OHRA', 'PETSECUR', 'UNIVE'].includes(company.logo) && (
                  <div className="text-2xl font-bold text-[#2F2E51]">
                    {company.logo}
                  </div>
                )}
              </div>
              <div className="text-lg font-semibold text-[#2F2E51]">
                {company.highlightText || company.name}
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-4">
              <span className="text-[#4DC166] text-lg">★</span>
              <span className="font-bold text-lg text-[#4DC166]">{company.rating}</span>
              <button
                onClick={() => setShowReviews(true)}
                className="text-[#2F2E51] text-sm underline hover:no-underline"
              >
                {company.reviewCount} beoordelingen
              </button>
            </div>

            {/* Features */}
            <div className="space-y-2 mb-6">
              {company.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-[#4DC166] mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  <span className="text-[#2F2E51] text-sm">{feature}</span>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-4">
              <a
                href={getInsuranceLink()}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-[#37396E] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#2F2E51] transition-colors"
              >
                Bekijken &gt;
              </a>
              {isHomepage && (company.name === 'Figo' || company.name === 'PetSecur') ? (
                <Link
                  href={company.name === 'Figo' ? "/verzekering/figo" : "/verzekering/petsecur"}
                  className="text-[#2F2E51] text-sm underline hover:no-underline"
                >
                  Meer informatie
                </Link>
              ) : (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-[#2F2E51] text-sm underline hover:no-underline"
                >
                  Meer informatie
                </button>
              )}
            </div>
          </div>

          {/* Right Section - Pricing */}
          <div className="w-80 bg-[#E8F5E8] p-6 flex flex-col justify-center">
            {/* Dog Pricing */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
                  <Image
                    src="/images/dog.webp"
                    alt="Hond"
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-[#2F2E51] font-medium">Vanaf</span>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-[#2F2E51]">
                  € {company.dogPrice}
                </div>
                <div className="text-sm text-gray-600">p/m</div>
              </div>
            </div>

            {/* Cat Pricing */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center overflow-hidden">
                  <Image
                    src="/images/cat.webp"
                    alt="Kat"
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-[#2F2E51] font-medium">Vanaf</span>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-[#2F2E51]">
                  € {company.catPrice}
                </div>
                <div className="text-sm text-gray-600">p/m</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="block md:hidden">
        <div className="p-4">
          {/* Logo and Company Name */}
          <div className="text-center mb-4">
            <div className="w-28 h-16 mx-auto mb-2 flex items-center justify-center">
              {company.logo === 'FIGO' && (
                <Image
                  src="/images/logos/figo-logo.svg"
                  alt="Figo Logo"
                  width={112}
                  height={64}
                  className="max-w-full max-h-full object-contain"
                />
              )}
              {company.logo === 'OHRA' && (
                <Image
                  src="/images/logos/ohra-logo.svg"
                  alt="OHRA Logo"
                  width={112}
                  height={64}
                  className="max-w-full max-h-full object-contain"
                />
              )}
              {company.logo === 'PETSECUR' && (
                <Image
                  src="/images/logos/petsecur-logo.png"
                  alt="PetSecur Logo"
                  width={112}
                  height={64}
                  className="max-w-full max-h-full object-contain"
                />
              )}
              {company.logo === 'UNIVE' && (
                <Image
                  src="/images/logos/unive-logo.svg"
                  alt="Univé Logo"
                  width={112}
                  height={64}
                  className="max-w-full max-h-full object-contain"
                />
              )}
              {!['FIGO', 'OHRA', 'PETSECUR', 'UNIVE'].includes(company.logo) && (
                <div className="text-xl font-bold text-[#2F2E51]">
                  {company.logo}
                </div>
              )}
            </div>
            <div className="text-sm font-semibold text-[#2F2E51]">
              {company.highlightText || company.name}
            </div>
          </div>

          {/* Rating */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <span className="text-[#4DC166] text-lg">★</span>
            <span className="font-bold text-lg text-[#4DC166]">{company.rating}</span>
            <button
              onClick={() => setShowReviews(true)}
              className="text-[#2F2E51] text-sm underline hover:no-underline"
            >
              {company.reviewCount} beoordelingen
            </button>
          </div>

          {/* Features */}
          <div className="space-y-2 mb-6">
            {company.features.map((feature, index) => (
              <div key={index} className="flex items-start gap-2">
                <svg className="w-4 h-4 text-[#4DC166] mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-[#2F2E51] text-sm">{feature}</span>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 mb-6">
            <a
              href={getInsuranceLink()}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-[#37396E] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#2F2E51] transition-colors text-center"
            >
              Bekijken &gt;
            </a>
            {isHomepage && (company.name === 'Figo' || company.name === 'PetSecur') ? (
              <Link
                href={company.name === 'Figo' ? "/verzekering/figo" : "/verzekering/petsecur"}
                className="text-[#2F2E51] text-sm underline hover:no-underline text-center block"
              >
                Meer informatie
              </Link>
            ) : (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-[#2F2E51] text-sm underline hover:no-underline text-center"
              >
                Meer informatie
              </button>
            )}
          </div>

          {/* Mobile Pricing */}
          <div className="bg-[#E8F5E8] rounded-lg p-4">
            {/* Dog Pricing */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
                  <Image
                    src="/images/dog.webp"
                    alt="Hond"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-[#2F2E51] text-sm font-medium">Vanaf</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-[#2F2E51]">
                  € {company.dogPrice}
                </div>
                <div className="text-xs text-gray-600">p/m</div>
              </div>
            </div>

            {/* Cat Pricing */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center overflow-hidden">
                  <Image
                    src="/images/cat.webp"
                    alt="Kat"
                    width={32}
                    height={32}
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-[#2F2E51] text-sm font-medium">Vanaf</span>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-[#2F2E51]">
                  € {company.catPrice}
                </div>
                <div className="text-xs text-gray-600">p/m</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Expandable Details Section */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="border-t border-gray-200 bg-gray-50 overflow-hidden"
          >
            <div className="p-6">
              <h4 className="text-xl font-bold text-[#2F2E51] mb-6">
                Dekking- en vergoedingsoverzicht
              </h4>

              <div className="grid md:grid-cols-2 gap-6">
                {/* Covered section */}
                <div>
                  <h5 className="text-lg font-semibold text-[#2F2E51] mb-4">
                    Verzekerd ({getPercentage()} vergoeding):
                  </h5>
                  <div className="space-y-3">
                    {company.coverage.covered.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200"
                      >
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-green-600 text-sm">✓</span>
                        </div>
                        <span className="text-[#2F2E51] text-base">
                          {item.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Not covered section */}
                <div>
                  <h5 className="text-lg font-semibold text-[#2F2E51] mb-4">
                    Niet verzekerd:
                  </h5>
                  <div className="space-y-3">
                    {company.coverage.notCovered.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200"
                      >
                        <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-red-600 text-sm">✗</span>
                        </div>
                        <span className="text-[#2F2E51] text-base">
                          {item.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Additional info */}
              <div className="mt-6 grid grid-cols-3 gap-4 p-4 bg-white rounded-lg border border-gray-200">
                <div className="text-center">
                  <div className="text-lg font-bold text-[#2F2E51]">{company.maxCoverage}</div>
                  <div className="text-sm text-gray-600">Max. vergoeding</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-[#2F2E51]">{company.ownRisk}</div>
                  <div className="text-sm text-gray-600">Eigen risico</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-[#2F2E51]">{company.monthlyFee}</div>
                  <div className="text-sm text-gray-600">Eenmalige kosten</div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Reviews Modal */}
      <ResponsiveReviewModal
        isOpen={showReviews}
        onClose={() => setShowReviews(false)}
        companyName={company.name}
        overallRating={company.rating}
        totalReviews={company.reviewCount}
      />
    </div>
  );
}

export default function BestBeoordeeld({ isHomepage = false }: { isHomepage?: boolean }) {
  return (
    <section className="pt-16 sm:pt-20 lg:pt-24 pb-12 bg-tea_green-900" data-section="best-beoordeeld">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div
            className="flex items-center justify-center gap-3 mb-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <span className="text-celadon-400 text-lg">★ ★</span>
            <h2 className="text-3xl font-bold text-celadon-100" data-section="top-verzekeraars">
              Top Nederlandse Dierenverzekeraars - Hoogst Gewaardeerd
            </h2>
            <span className="text-celadon-400 text-lg">★ ★</span>
          </motion.div>
          <motion.p
            className="text-lg text-celadon-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            Bekijk en vergelijk de belangrijkste Nederlandse dierenverzekeraars op basis van dekking, tarieven en klantwaardering.
            Onze specialisten evalueren iedere aanbieder aan de hand van prijs-kwaliteitverhouding,
            dekkingsomvang en efficiëntie van claimafhandeling.
          </motion.p>
        </motion.div>



        {/* Insurance Cards */}
        <div className="space-y-4 mb-8">
          {insuranceCompanies.map((company, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 + (index * 0.2), ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <InsuranceCard company={company} isHomepage={isHomepage} />
            </motion.div>
          ))}
        </div>

        {/* Bottom buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-6 mb-16">
          <a
            href="/hondenverzekering"
            className="group relative overflow-hidden bg-gradient-to-r from-[#2F2E51] to-[#3d3c6b] text-white px-10 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out min-w-[140px] border-2 border-transparent hover:border-white/20 text-center"
          >
            <span className="relative z-10">Hond</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#3d3c6b] to-[#2F2E51] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
          <a
            href="/kattenverzekering"
            className="group relative overflow-hidden bg-gradient-to-r from-[#2F2E51] to-[#3d3c6b] text-white px-10 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out min-w-[140px] border-2 border-transparent hover:border-white/20 text-center"
          >
            <span className="relative z-10">Kat</span>
            <div className="absolute inset-0 bg-gradient-to-r from-[#3d3c6b] to-[#2F2E51] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
        </div>

        {/* Modern Why Compare Section */}
        <ModernComparisonBenefits />

        {/* Specific comparison examples */}
        <div className="grid sm:grid-cols-2 gap-6">
            {/* Dog comparison */}
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <h4 className="text-xl font-bold text-[#2F2E51]">
                  Hondenverzekering vergelijken - beste verzekering voor honden vanaf €17 per maand
                </h4>
              </div>
              <p className="text-gray-600 mb-4">
                Vergelijk alle Nederlandse hondenverzekeringen. Vind de beste dekking voor jouw hond 
                tegen de laagste premie.
              </p>
              <a 
                href="/hondenverzekering" 
                className="inline-flex items-center gap-2 text-[#2F2E51] font-semibold hover:text-slate-700"
              >
                Hondenverzekering vergelijken
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {/* Cat comparison */}
            <div className="bg-white p-6 rounded-xl shadow-lg">
              <div className="flex items-center gap-3 mb-4">
                <h4 className="text-xl font-bold text-[#2F2E51]">
                  Kattenverzekering vergelijken - beste verzekering voor katten vanaf €11 per maand
                </h4>
              </div>
              <p className="text-gray-600 mb-4">
                Vergelijk alle Nederlandse kattenverzekeringen. Vind de beste dekking voor jouw kat 
                tegen de laagste premie.
              </p>
              <a 
                href="/kattenverzekering" 
                className="inline-flex items-center gap-2 text-[#2F2E51] font-semibold hover:text-slate-700"
              >
                Kattenverzekering vergelijken
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>

        {/* Modern Conclusion Section */}
        <ModernConclusionSection />

        {/* Alle verzekeraars section */}
        <div className="bg-gray-50 py-12 rounded-lg">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-[#2F2E51] mb-4">
              Alle Nederlandse Dierenverzekeraars Vergelijken
            </h3>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Bekijk ons complete overzicht met alle aanbieders van huisdierenverzekeringen in Nederland.
              Vergelijk premies, dekking en voorwaarden van OHRA, PetSecur, Figo, Univé, InShared, ASR en
              Dierenverzekering.nl om jouw beste keuze te maken.
            </p>
          </div>

          {/* Insurance company logos grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8 max-w-4xl mx-auto">
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-orange-500 mb-2">PetSecur</div>
              <div className="text-xs sm:text-sm text-gray-600">PetSecur</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-800 mb-2">FIGO</div>
              <div className="text-xs sm:text-sm text-gray-600">Figo</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-600 border-2 border-blue-600 px-2 py-1 rounded mb-2">OHRA</div>
              <div className="text-xs sm:text-sm text-gray-600">OHRA</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-pink-500 mb-2">inshared</div>
              <div className="text-xs sm:text-sm text-gray-600">Inshared</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-green-600 mb-2">univé</div>
              <div className="text-xs sm:text-sm text-gray-600">Univé</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-sm sm:text-lg lg:text-xl font-bold text-slate-800 mb-2 text-center">DIERENVERZEKERING</div>
              <div className="text-xs sm:text-sm text-gray-600 text-center">Dierenverzekering.nl</div>
            </div>
            <div className="flex flex-col items-center p-3 sm:p-4 lg:p-6 bg-white rounded-lg">
              <div className="text-lg sm:text-xl text-gray-600 mb-2">★★★</div>
              <div className="text-xs sm:text-sm text-gray-600">AAL</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}