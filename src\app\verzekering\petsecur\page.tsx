"use client"

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

interface AnimalData {
  name: string
  image: string
  tabImage: string
  pricing: {
    basis: { price: string, cents: string }
    standaard: { price: string, cents: string }
    extra: { price: string, cents: string }
  }
}

const animalData: Record<string, AnimalData> = {
  hond: {
    name: 'Hond',
    image: '/images/insurance-details/hond-tab.jpg',
    tabImage: '/images/insurance-details/hond-tab.jpg',
    pricing: {
      basis: { price: '18', cents: '47' },
      standaard: { price: '27', cents: '84' },
      extra: { price: '36', cents: '23' }
    }
  },
  kat: {
    name: 'Kat',
    image: '/images/insurance-details/kat-tab.jpg',
    tabImage: '/images/insurance-details/kat-tab.jpg',
    pricing: {
      basis: { price: '16', cents: '28' },
      standaard: { price: '21', cents: '82' },
      extra: { price: '25', cents: '07' }
    }
  }
}

export default function PetSecurInsurancePage() {
  const [selectedAnimal, setSelectedAnimal] = useState<string>('hond')
  const currentAnimal = animalData[selectedAnimal]

  const getPetSecurAffiliateLink = () => {
    return 'https://fr135.net/c/?si=15431&li=1668064&wi=411525&ws=zoekdierenverzekering&dl=%2F';
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section - Match Figo Style */}
      <section className="relative bg-tea_green-900 pt-32 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold text-celadon-100 mb-6">
                PetSecur Dierenverzekering
              </h1>
              <div className="flex items-center gap-3 mb-8">
                <svg className="w-6 h-6 text-celadon-100" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-xl text-celadon-100">Snelle afhandeling binnen 5 werkdagen</span>
              </div>
            </div>

            {/* Right Content - Rating */}
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-white">
                <div className="text-center">
                  <div className="text-4xl font-bold text-tea_green-100 mb-2">7.9</div>
                  <button className="text-sm text-gray-600 hover:text-tea_green-100 transition-colors">
                    70 beoordelingen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-tea_green-100 text-center mb-12">
            Vergelijk alle specificaties
          </h2>

          {/* Animal Selection Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {Object.entries(animalData).map(([key, animal]) => (
              <button
                key={key}
                onClick={() => setSelectedAnimal(key)}
                className={`flex items-center rounded-lg border-2 overflow-hidden transition-all duration-300 ${
                  selectedAnimal === key
                    ? 'border-tea_green-100 bg-tea_green-100 text-white shadow-lg'
                    : 'border-gray-300 bg-white text-tea_green-100 hover:border-tea_green-200'
                }`}
              >
                <div className="w-20 h-16 relative">
                  <Image
                    src={animal.tabImage}
                    alt={animal.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="px-4 py-3">
                  <span className="font-semibold">{animal.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* Content Area with Fade Transition */}
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedAnimal}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-8"
            >
              {/* Pricing Cards - PetSecur Design */}
              <div className="grid md:grid-cols-3 gap-6">
                {/* Basis Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with PetSecur Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/petsecur-logo.png"
                        alt="PetSecur"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">Basis</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.250,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">6 jaar</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.basis.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.basis.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getPetSecurAffiliateLink()}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* Standaard Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/petsecur-logo.png"
                        alt="PetSecur"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">Standaard</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 5.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">6 jaar</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.standaard.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.standaard.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getPetSecurAffiliateLink()}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* Extra Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/petsecur-logo.png"
                        alt="PetSecur"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">Extra</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 12.500,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">6 jaar</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.extra.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.extra.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getPetSecurAffiliateLink()}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>
              </div>

              {/* Compare Button */}
              <div className="text-center">
                <Link
                  href="/"
                  className="btn-cta-outline inline-block font-bold"
                >
                  Vergelijk huisdierverzekeringen
                </Link>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      <Footer />
    </div>
  )
}
