"use client"

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { artikelsArticles } from '@/data/artikelsArticles'

export default function ArticlesSection() {
  // Get the 3 newest articles sorted by publishedDate
  const newestArticles = artikelsArticles
    .sort((a, b) => new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime())
    .slice(0, 3)
  return (
    <section className="w-full bg-tea_green-800 py-8 sm:py-16 mt-4 sm:mt-0">
      <div className="max-w-[1284px] mx-auto px-4">
        {/* Title - Responsive */}
        <motion.h2
          className="text-[24px] sm:text-[32px] md:text-[42px] font-bold text-celadon-100 text-center leading-[1.2] mb-8 sm:mb-12 md:mb-16 font-figtree"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          Onze nieuwe artikelen over
          <br />
          huisdierenverzekeringen
        </motion.h2>

        {/* Desktop Layout */}
        <div className="hidden md:grid md:grid-cols-3 gap-[30px]">
          {newestArticles.map((article, index) => (
            <motion.div
              key={article.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 + (index * 0.2), ease: "easeOut" }}
              viewport={{ once: true, margin: "-50px" }}
            >
              <Link href={`/artikels/${article.slug}`} className="flex flex-col gap-[30px] cursor-pointer hover:opacity-80 transition-opacity group">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col gap-[30px]"
                >
                  {/* Large image card */}
                  <div className="w-[398px] h-[265px] rounded-[10px] overflow-hidden">
                    <Image
                      src={article.image || "/images/artikels/verzekeren/insurance-paperwork.jpg"}
                      alt={article.title}
                      width={398}
                      height={265}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 398px"
                    />
                  </div>

                  {/* Text card below */}
                  <div className="w-[398px] min-h-[61px] flex items-center">
                    <h3 className="text-[24px] font-bold text-celadon-100 leading-[1.2] font-figtree">
                      {article.title}
                    </h3>
                  </div>
                </motion.div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Mobile Layout */}
        <div className="block md:hidden space-y-6">
          {newestArticles.map((article, index) => (
            <motion.div
              key={`mobile-${article.id}`}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 + (index * 0.2), ease: "easeOut" }}
              viewport={{ once: true, margin: "-30px" }}
            >
              <Link href={`/artikels/${article.slug}`} className="w-full max-w-[360px] mx-auto block cursor-pointer hover:opacity-80 transition-opacity">
                <div className="w-full h-[200px] rounded-[10px] overflow-hidden mb-4">
                  <Image
                    src={article.image || "/images/artikels/verzekeren/insurance-paperwork.jpg"}
                    alt={article.title}
                    width={360}
                    height={200}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-[18px] sm:text-[20px] font-bold text-[#2F2E51] leading-[1.2] font-figtree px-2">
                  {article.title}
                </h3>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* View all button - Responsive */}
        <motion.div
          className="flex justify-center mt-8 sm:mt-12 md:mt-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7, ease: "easeOut" }}
          viewport={{ once: true, margin: "-50px" }}
        >
          <Link href="/kennisbank">
            <motion.button
              className="bg-white border border-celadon-100 text-celadon-100 rounded-full font-bold text-[16px] sm:text-[19px] hover:bg-celadon-100 hover:text-white transition-colors font-figtree w-[150px] sm:w-[176px] h-[50px] sm:h-[61px] flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              Alle artikelen
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}