import { Metadata } from 'next'

interface PetMetadataParams {
  petType: string
  petTypeLower: string
  minPrice: string
  url: string
}

export function generatePetInsuranceMetadata({
  petType,
  petTypeLower,
  minPrice,
  url
}: PetMetadataParams): Metadata {
  // Generate proper plurals
  const getPluralForm = (pet: string) => {
    switch(pet.toLowerCase()) {
      case 'hond': return 'Honden'
      case 'kat': return 'Katten'
      case 'konijn': return 'Konijnen'
      case 'papegaai': return 'Papegaaien'
      default: return `${pet}en`
    }
  }
  
  const petPlural = getPluralForm(petType)
  const title = `${petType}verzekering Vergelijken 2025 - Beste Verzekering voor ${petPlural}`
  const description = `Vergelijk alle Nederlandse ${petTypeLower}verzekeringen. Vind de beste dekking voor jouw ${petTypeLower} tegen de laagste premie. Vanaf ${minPrice} per maand.`
  
  const keywords = [
    `${petTypeLower}verzekering`,
    `${petTypeLower}verzekering vergelijken`,
    `beste ${petTypeLower}verzekering`,
    `goedkoopste ${petTypeLower}verzekering`,
    `${petTypeLower} verzekeren`,
    `${petTypeLower}verzekering kosten`,
    `Nederlandse ${petTypeLower}verzekeraars`
  ]
  
  // Add specific keywords for certain pet types
  if (petTypeLower === 'papegaai') {
    keywords.push('vogelverzekering')
  }

  return {
    title,
    description,
    keywords,
    alternates: {
      canonical: `https://zoekdierenverzekering.nl/${url}`,
    },
    openGraph: {
      title,
      description,
      url: `https://zoekdierenverzekering.nl/${url}`,
      siteName: "ZoekDierenverzekering.nl",
      locale: "nl_NL",
      type: "website"
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  }
}