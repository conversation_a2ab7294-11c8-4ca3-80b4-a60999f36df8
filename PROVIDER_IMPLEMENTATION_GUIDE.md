# Insurance Provider Implementation Guide

This guide provides step-by-step instructions for adding a new insurance provider to the dierenverzekering comparison website. Follow these steps exactly to ensure consistency and proper functionality.

## Prerequisites

Before starting, you need:
1. The provider's affiliate link from `affiliate-links.md`
2. The provider's logo file in `public/images/logos/`
3. Access to the provider's information page (e.g., from a comparison website)

## Step 1: Add Provider to Homepage Cards

### 1.1 Update BestBeoordeeld.tsx

**File:** `src/components/BestBeoordeeld.tsx`

Add the new provider to the `insuranceCompanies` array. Use this template:

```typescript
{
  name: "ProviderName",
  rating: X.X, // Provider's rating
  reviewCount: XX, // Number of reviews
  features: [
    "Key feature 1",
    "Key feature 2 (optional)"
  ],
  logo: "PROVIDER_LOGO_NAME", // Must match logo filename
  dogPrice: "XX,XX", // Dog insurance price
  catPrice: "XX,XX", // Cat insurance price
  maxCoverage: "€ X.XXX,XX", // Maximum coverage amount
  ownRisk: "Geen", // Or specific amount
  monthlyFee: "€ 0,00", // Usually 0
  coverage: {
    covered: [
      { name: "Consult dierenarts", covered: true, percentage: "XX%" },
      { name: "Diergeneesmiddelen", covered: true, percentage: "XX%" },
      { name: "Operaties", covered: true, percentage: "XX%" },
      // Add all covered items with percentages
    ],
    notCovered: [
      { name: "Service not covered", covered: false },
      // Add items not covered
    ]
  }
}
```

### 1.2 Update Helper Functions

Add the provider to these functions in `BestBeoordeeld.tsx`:

**getPercentage function:**
```typescript
const getPercentage = () => {
  if (company.logo === 'FIGO') return '50%'
  if (company.logo === 'PETSECUR') return '50%'
  if (company.logo === 'NEW_PROVIDER') return 'XX%' // Add this line
  if (company.logo === 'OHRA') return '80%'
  return '50%'
}
```

**Affiliate link function:**
```typescript
const getNewProviderAffiliateLink = () => {
  // Copy affiliate link from affiliate-links.md
  return 'AFFILIATE_LINK_HERE';
}

const getInsuranceLink = () => {
  if (company.name === 'Figo') {
    return getFigoAffiliateLink();
  }
  if (company.name === 'PetSecur') {
    return getPetSecurAffiliateLink();
  }
  if (company.name === 'NewProvider') { // Add this block
    return getNewProviderAffiliateLink();
  }
  return '#';
}
```

**"Meer informatie" links:**
```typescript
{isHomepage && (company.name === 'Figo' || company.name === 'PetSecur' || company.name === 'NewProvider') ? (
  <Link
    href={
      company.name === 'Figo' ? "/verzekering/figo" : 
      company.name === 'PetSecur' ? "/verzekering/petsecur" :
      "/verzekering/newprovider" // Add this line
    }
    className="text-[#2F2E51] text-sm underline hover:no-underline"
  >
    Meer informatie
  </Link>
```

## Step 2: Update Insurance Count

**File:** `src/components/PetInsurancePage.tsx`

Update the count display:
```typescript
<p className="text-xl text-celadon-100 mb-8">
  X verzekeringen gevonden // Increment the number
</p>
```

## Step 3: Create Provider Detail Page

### 3.1 Create Directory Structure
```
src/app/verzekering/providername/page.tsx
```

### 3.2 Copy Template
Use `src/app/verzekering/petsecur/page.tsx` as your template. Make these key changes:

**Component name:**
```typescript
export default function ProviderNameInsurancePage() {
```

**Animal data pricing:**
```typescript
const animalData: Record<string, AnimalData> = {
  hond: {
    name: 'Hond',
    image: '/images/insurance-details/hond-tab.jpg',
    tabImage: '/images/insurance-details/hond-tab.jpg',
    pricing: {
      basis: { price: 'XX', cents: 'XX' },
      standaard: { price: 'XX', cents: 'XX' },
      extra: { price: 'XX', cents: 'XX' }
    }
  },
  kat: {
    // Similar structure with cat pricing
  }
}
```

**Affiliate link function:**
```typescript
const getProviderAffiliateLink = () => {
  return 'AFFILIATE_LINK_FROM_AFFILIATE_LINKS_MD';
}
```

**Hero section:**
```typescript
<h1 className="text-5xl lg:text-6xl font-bold text-celadon-100 mb-6">
  ProviderName Dierenverzekering
</h1>
<span className="text-xl text-celadon-100">Provider's key selling point</span>
```

**Rating section:**
```typescript
<div className="text-4xl font-bold text-tea_green-100 mb-2">X.X</div>
<button className="text-sm text-gray-600 hover:text-tea_green-100 transition-colors">
  XX beoordelingen
</button>
```

**Logo references:**
Replace all instances of:
```typescript
src="/images/logos/petsecur-logo.png"
alt="PetSecur"
```
With:
```typescript
src="/images/logos/providername-logo.png"
alt="ProviderName"
```

## Step 4: Content Adaptation

### 4.1 Gather Source Information
1. Visit the provider's page on the comparison website
2. Extract key information:
   - Pricing for different packages
   - Coverage details
   - Maximum coverage amounts
   - Age limits
   - Waiting periods
   - What's covered/not covered

### 4.2 Rewrite Content
**Important:** Rewrite all content to avoid copyright issues while preserving:
- SEO keywords
- Factual accuracy
- Key selling points
- Coverage details

### 4.3 Update Sections

**Why Choose Provider section:**
```typescript
<h2 className="text-3xl font-bold text-tea_green-100 mb-6">
  Wat maakt ProviderName de juiste keuze voor jouw huisdier?
</h2>
<p className="text-gray-700 mb-6">
  [Rewritten description of provider's strengths and unique selling points]
</p>
```

**About Provider section:**
```typescript
<h2 className="text-3xl font-bold text-tea_green-100 mb-8 text-center">
  Kennismaking met ProviderName als verzekeraar
</h2>
```

**FAQ section:**
Update questions and answers to be provider-specific.

**Reviews section:**
```typescript
<h2 className="text-3xl font-bold text-tea_green-100 mb-8">
  Beoordelingen van ProviderName
</h2>
<div className="text-6xl font-bold text-tea_green-100 mb-4">X.X</div>
<p className="text-gray-600 mb-6">Op basis van XX klantbeoordelingen</p>
```

## Step 5: Implement Comprehensive Tables

### 5.1 Kosten & beperkingen Table
Update the specifications table with provider-specific data:

```typescript
<tbody className="text-sm">
  <tr className="border-b border-gray-100">
    <td className="py-4 px-6 text-gray-700 font-medium">Max. vergoeding per jaar</td>
    <td className="py-4 px-4 text-center text-gray-900">€ X.XXX,XX</td>
    <td className="py-4 px-4 text-center text-gray-900">€ X.XXX,XX</td>
    <td className="py-4 px-4 text-center text-gray-900">€ X.XXX,XX</td>
  </tr>
  // Continue with all rows...
</tbody>
```

### 5.2 Coverage Table
Create a comprehensive "Wat is gedekt" table showing all covered and non-covered items:

```typescript
<tr className="border-b border-gray-100">
  <td className="py-4 px-6 text-gray-700 font-medium">Treatment name</td>
  <td className="py-4 px-4 text-center text-green-600 font-bold">✓ XX%</td>
  <td className="py-4 px-4 text-center text-red-600 font-bold">✗</td>
  <td className="py-4 px-4 text-center text-green-600 font-bold">✓ XX%</td>
</tr>
```

## Step 6: Testing Protocol

After implementation, test systematically:

### 6.1 Desktop Testing (1920x1080)
- [ ] Homepage shows new provider card
- [ ] Provider card displays correct information
- [ ] "Meer info" button links to detail page
- [ ] "Bekijken" button uses correct affiliate link
- [ ] Detail page loads without errors
- [ ] Animal switching works correctly
- [ ] Pricing updates properly
- [ ] All affiliate links work
- [ ] Tables display correctly
- [ ] Responsive design works

### 6.2 Mobile Testing (375x812)
- [ ] All desktop tests pass on mobile
- [ ] Touch targets are minimum 44px
- [ ] Tables scroll horizontally if needed
- [ ] Text remains readable
- [ ] Buttons are easily tappable

### 6.3 Functionality Testing
- [ ] No console errors
- [ ] All images load correctly
- [ ] Smooth animations work
- [ ] FAQ sections expand/collapse
- [ ] Navigation works properly

## Step 7: SEO and Content Verification

### 7.1 Content Checklist
- [ ] All content rewritten to avoid copyright issues
- [ ] SEO keywords preserved
- [ ] Factual accuracy maintained
- [ ] No references to source comparison website
- [ ] Provider-specific information accurate
- [ ] Pricing information current

### 7.2 Technical Checklist
- [ ] Affiliate tracking properly implemented
- [ ] All links functional
- [ ] Images optimized and loading
- [ ] Page title and meta descriptions updated
- [ ] Accessibility standards met (WCAG AA)

## Common Pitfalls to Avoid

1. **Copyright Issues:** Always rewrite content, never copy directly
2. **Affiliate Links:** Double-check affiliate IDs and tracking parameters
3. **Pricing Accuracy:** Verify all pricing information is current
4. **Logo Quality:** Ensure logos are high-resolution and properly sized
5. **Mobile Responsiveness:** Test thoroughly on mobile devices
6. **Table Structure:** Ensure tables are vertically stacked and responsive
7. **Content Consistency:** Maintain the same tone and style as existing providers

## Final Notes

- Always test the complete user journey from homepage to affiliate link
- Verify that the provider appears in all relevant sections
- Ensure the implementation matches the existing design patterns exactly
- Document any provider-specific requirements or limitations
- Keep affiliate links and provider information up to date

This guide ensures consistent implementation of new insurance providers while maintaining the website's quality standards and user experience.
