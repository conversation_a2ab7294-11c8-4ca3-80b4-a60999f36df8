'use client'

import { useEffect } from 'react'

// Extend window object to include Cookiebot types
declare global {
  interface Window {
    Cookiebot?: {
      consent?: {
        necessary: boolean
        preferences: boolean
        statistics: boolean
        marketing: boolean
      }
      show?: () => void
      hide?: () => void
      withdraw?: () => void
      consented?: boolean
    }
    gtag?: (...args: unknown[]) => void
    dataLayer?: unknown[]
  }
}

// Update Google Consent Mode based on Cookiebot consent via GTM dataLayer
const updateGoogleConsent = (consent: {
  necessary: boolean
  preferences: boolean
  statistics: boolean
  marketing: boolean
}) => {
  if (typeof window !== 'undefined') {
    // Use GTM dataLayer if available, fallback to gtag
    if (window.dataLayer) {
      window.dataLayer.push({
        event: 'consent_update',
        consent: {
          ad_personalization: consent.marketing ? 'granted' : 'denied',
          ad_storage: consent.marketing ? 'granted' : 'denied',
          ad_user_data: consent.marketing ? 'granted' : 'denied',
          analytics_storage: consent.statistics ? 'granted' : 'denied',
          functionality_storage: consent.preferences ? 'granted' : 'denied',
          personalization_storage: consent.preferences ? 'granted' : 'denied',
        }
      })
    } else if (window.gtag) {
      // Fallback to direct gtag call
      window.gtag('consent', 'update', {
        ad_personalization: consent.marketing ? 'granted' : 'denied',
        ad_storage: consent.marketing ? 'granted' : 'denied',
        ad_user_data: consent.marketing ? 'granted' : 'denied',
        analytics_storage: consent.statistics ? 'granted' : 'denied',
        functionality_storage: consent.preferences ? 'granted' : 'denied',
        personalization_storage: consent.preferences ? 'granted' : 'denied',
      })
    }
    
    console.log('Google Consent Mode updated via GTM:', {
      analytics_storage: consent.statistics ? 'granted' : 'denied',
      ad_storage: consent.marketing ? 'granted' : 'denied',
      functionality_storage: consent.preferences ? 'granted' : 'denied'
    })
  }
}

// Cookiebot consent manager
export const CookiebotManager = {
  // Check if consent is given for a category
  hasConsent: (category: 'necessary' | 'preferences' | 'statistics' | 'marketing'): boolean => {
    if (typeof window === 'undefined' || !window.Cookiebot?.consent) {
      return false
    }
    return window.Cookiebot.consent[category] || false
  },

  // Initialize tracking based on consent
  initializeTracking: () => {
    if (typeof window === 'undefined' || !window.Cookiebot?.consent) return

    const consent = window.Cookiebot.consent
    console.log('Initializing tracking based on consent:', consent)

    // Update Google Consent Mode with current consent state
    updateGoogleConsent(consent)
  },

  // Handle consent changes
  onConsentChanged: () => {
    if (typeof window === 'undefined' || !window.Cookiebot?.consent) return
    
    const consent = window.Cookiebot.consent
    console.log('Consent changed:', consent)
    
    // Update Google Consent Mode with new consent state
    updateGoogleConsent(consent)
  }
}

// React component for Cookiebot consent management
export default function CookiebotConsent() {
  useEffect(() => {
    // Set up Cookiebot event listeners
    const handleConsentReady = () => {
      console.log('Cookiebot consent ready')
      CookiebotManager.initializeTracking()
    }

    const handleConsentChanged = () => {
      console.log('Cookiebot consent changed')
      CookiebotManager.onConsentChanged()
    }

    // Add event listeners
    window.addEventListener('CookiebotOnConsentReady', handleConsentReady)
    window.addEventListener('CookiebotOnConsentChanged', handleConsentChanged)

    // Check if Cookiebot is already loaded and initialized
    if (window.Cookiebot?.consent) {
      handleConsentReady()
    }

    // Cleanup event listeners
    return () => {
      window.removeEventListener('CookiebotOnConsentReady', handleConsentReady)
      window.removeEventListener('CookiebotOnConsentChanged', handleConsentChanged)
    }
  }, [])

  return null // This is a utility component with no UI
}