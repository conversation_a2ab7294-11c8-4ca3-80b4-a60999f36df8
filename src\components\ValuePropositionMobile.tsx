"use client"

import Image from "next/image";
import ModernFactsAndTestimonials from "./ModernFactsAndTestimonials";
import CheckIcon from "./ui/CheckIcon";

export default function ValuePropositionMobile() {
  return (
    <section className="block sm:hidden bg-white">
      {/* Hero Image Section - Node 27:56 */}
      <div className="w-full px-4">
        <div className="relative max-w-[295px] aspect-[295/374] mx-auto rounded-[10px] overflow-hidden">
          <Image
            src="/images/hero.webp"
            alt="Huisdierenverzekering"
            width={295}
            height={374}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Container Section - Node 27:57 */}
      <div className="w-full max-w-[390px] mx-auto px-4 py-4 bg-white">
        {/* Title */}
        <h2 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
          Daarom kies je voor een{'\n'}
          dierenverzekering
        </h2>
        
        {/* Description */}
        <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px] mb-8">
          Een huisdier brengt zorgkosten met zich mee.{'\n'}
          Iedere hond of kat moet wel eens naar de dokter,{'\n'}
          net als jijzelf. Voorkom financiële verrassingen bij{'\n'}
          de dierenarts en sluit een passende{'\n'}
          dierenverzekering af voor je trouwe dierenvriend.
        </p>

        {/* List Section */}
        <div className="space-y-0">
          {[
            "Vergelijk Figo en OHRA",
            "Alle kleine lettertjes op een rij", 
            "Reviews van andere huisdiereigenaren"
          ].map((text, index) => (
            <div key={index} className={`flex items-center h-[36px] ${index < 2 ? 'mb-0' : ''}`}>
              <div className="w-[18px] h-[18px] mr-[9.5px] mt-2 flex-shrink-0">
                <CheckIcon />
              </div>
              <span className="text-[16px] font-normal text-[#2F2E51] leading-[36px]">
                {text}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Slim vergelijken Section - Node 27:76 */}
      <div className="w-full max-w-[390px] mx-auto px-4 py-4 bg-white">
        <h3 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-4">
          Slim vergelijken op{'\n'}
          ZoekDierenverzekering.nl
        </h3>
        <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
          Bekijk, vergelijk en kies in een paar klikken de{'\n'}
          perfecte verzekering voor jouw viervoeter.
        </p>
      </div>

      {/* Actuele en correcte informatie Section - Node 27:79 */}
      <div className="w-[360px] h-[572.25px] mx-auto bg-white border-b border-[#E5E5E5]">
        {/* Image */}
        <div className="w-full h-[240.11px] rounded-[10px] overflow-hidden mb-0">
          <Image
            src="/images/man-about-to-kiss-a-cute-puppy-dog-outdoors-2025-03-07-23-33-25-utc.webp"
            alt="Vrouw en hond"
            width={360}
            height={240}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Content */}
        <div className="px-[15px] pt-4 pb-4">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
            Actuele en correcte informatie
          </h4>
          <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
            Je huisdier is een belangrijk onderdeel van je{'\n'}
            gezin. Dat gezinslid wil je graag de best mogelijke{'\n'}
            zorg kunnen geven. Wij weten dat als geen ander{'\n'}
            en daarom helpen we je graag. Met informatie{'\n'}
            waarop je kunt vertrouwen. Samen met onze{'\n'}
            experts zorgen we ervoor dat alle{'\n'}
            verzekeringsvoorwaarden, premies en andere{'\n'}
            gegevens actueel zijn. En je vindt bij ons de juiste{'\n'}
            tips en adviezen.
          </p>
        </div>
      </div>

      {/* Onafhankelijk vergelijken Section - Node 27:84 */}
      <div className="w-[360px] h-[621.84px] mx-auto bg-white border-b border-[#E5E5E5]">
        {/* Image */}
        <div className="w-full h-[240.11px] rounded-[10px] overflow-hidden mt-6 mb-0">
          <Image
            src="/images/cat.webp"
            alt="Vrouw en kat"
            width={360}
            height={240}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Content */}
        <div className="px-[15px] pt-4 pb-4">
          <h4 className="text-[24px] font-bold text-[#2F2E51] leading-[28.8px] mb-6">
            Transparant vergelijken
          </h4>
          <p className="text-[16px] font-normal text-[#2F2E51] leading-[25.6px]">
            ZoekDierenverzekering.nl is een vergelijkingsplatform{'\n'}
            dat momenteel Figo en OHRA dierenverzekeringen{'\n'}
            vergelijkt. Ons team houdt alle informatie op de{'\n'}
            website accuraat en actueel. In onze overzichtelijke{'\n'}
            vergelijker kan je vervolgens makkelijk{'\n'}
            dierenverzekeringen bekijken, vergelijken en{'\n'}
            jouw beste keuze maken. We ontvangen commissie{'\n'}
            bij afsluiting van verzekeringen.
          </p>
        </div>
      </div>

      {/* Modern Facts and Testimonials Section */}
      <ModernFactsAndTestimonials className="block sm:hidden" />
    </section>
  );
}