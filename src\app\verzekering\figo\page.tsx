"use client"

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

interface AnimalData {
  name: string
  image: string
  tabImage: string
  pricing: {
    percentage50: { price: string, cents: string }
    percentage70: { price: string, cents: string }
    percentage90: { price: string, cents: string }
  }
}

const animalData: Record<string, AnimalData> = {
  hond: {
    name: 'Hond',
    image: '/images/insurance-details/hond-tab.jpg',
    tabImage: '/images/insurance-details/hond-tab.jpg',
    pricing: {
      percentage50: { price: '17', cents: '25' },
      percentage70: { price: '28', cents: '47' },
      percentage90: { price: '41', cents: '92' }
    }
  },
  kat: {
    name: 'Kat',
    image: '/images/insurance-details/kat-tab.jpg',
    tabImage: '/images/insurance-details/kat-tab.jpg',
    pricing: {
      percentage50: { price: '11', cents: '41' },
      percentage70: { price: '17', cents: '88' },
      percentage90: { price: '31', cents: '19' }
    }
  },
  konijn: {
    name: 'Konijn',
    image: '/images/insurance-details/konijn-tab.jpg',
    tabImage: '/images/insurance-details/konijn-tab.jpg',
    pricing: {
      percentage50: { price: '12', cents: '83' },
      percentage70: { price: '18', cents: '78' },
      percentage90: { price: '27', cents: '09' }
    }
  },
  papegaai: {
    name: 'Papegaai',
    image: '/images/insurance-details/papegaai-tab.jpg',
    tabImage: '/images/insurance-details/papegaai-tab.jpg',
    pricing: {
      percentage50: { price: '19', cents: '28' },
      percentage70: { price: '27', cents: '45' },
      percentage90: { price: '34', cents: '79' }
    }
  }
}

export default function FigoInsurancePage() {
  const [selectedAnimal, setSelectedAnimal] = useState<string>('hond')
  const currentAnimal = animalData[selectedAnimal]

  const getFigoAffiliateLink = (animal: string) => {
    const baseUrl = 'https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&clickref=zoekdierenverzekering.nl&ued='
    const animalUrls = {
      hond: 'https%3A%2F%2Ffigopet.nl%2Fhondenverzekering%2F',
      kat: 'https%3A%2F%2Ffigopet.nl%2Fkattenverzekering%2F',
      konijn: 'https%3A%2F%2Ffigopet.nl%2Fkonijnenverzekering%2F',
      papegaai: 'https%3A%2F%2Ffigopet.nl%2Fpapegaaienverzekering%2F'
    }
    return baseUrl + (animalUrls[animal as keyof typeof animalUrls] || animalUrls.hond)
  }

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section - Match Homepage Style */}
      <section className="relative bg-tea_green-900 pt-32 pb-16 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold text-celadon-100 mb-6">
                Figo Dierenverzekering
              </h1>
              <div className="flex items-center gap-3 mb-8">
                <svg className="w-6 h-6 text-celadon-100" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                <span className="text-xl text-celadon-100">Welkom voor alle rassen en leeftijden</span>
              </div>
            </div>

            {/* Right Content - Rating */}
            <div className="flex justify-center lg:justify-end">
              <div className="bg-white rounded-2xl p-6 shadow-xl border-2 border-white">
                <div className="text-center">
                  <div className="text-4xl font-bold text-tea_green-100 mb-2">7.9</div>
                  <button className="text-sm text-gray-600 hover:text-tea_green-100 transition-colors">
                    41 beoordelingen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold text-tea_green-100 text-center mb-12">
            Vergelijk alle specificaties
          </h2>

          {/* Animal Selection Tabs */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {Object.entries(animalData).map(([key, animal]) => (
              <button
                key={key}
                onClick={() => setSelectedAnimal(key)}
                className={`flex items-center rounded-lg border-2 overflow-hidden transition-all duration-300 ${
                  selectedAnimal === key
                    ? 'border-tea_green-100 bg-tea_green-100 text-white shadow-lg'
                    : 'border-gray-300 bg-white text-tea_green-100 hover:border-tea_green-200'
                }`}
              >
                <div className="w-20 h-16 relative">
                  <Image
                    src={animal.tabImage}
                    alt={animal.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="px-4 py-3">
                  <span className="font-semibold">{animal.name}</span>
                </div>
              </button>
            ))}
          </div>

          {/* Content Area with Fade Transition */}
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedAnimal}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-8"
            >
              {/* Pricing Cards - Figma Design */}
              <div className="grid md:grid-cols-3 gap-6">
                {/* 50% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">50%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage50.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage50.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* 70% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">70%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage70.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage70.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>

                {/* 90% Coverage Card */}
                <div className="bg-white border border-gray-300 rounded-lg overflow-hidden shadow-sm">
                  {/* Header with Figo Logo */}
                  <div className="bg-white border-b border-gray-300 p-6 text-center">
                    <div className="mb-4">
                      <Image
                        src="/images/logos/figo-logo.svg"
                        alt="Figo"
                        width={168}
                        height={84}
                        className="mx-auto"
                      />
                    </div>
                  </div>

                  {/* Coverage Details */}
                  <div className="p-6 space-y-4">
                    <h3 className="text-2xl font-bold text-tea_green-100 mb-4">90%</h3>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-700">Verzekerd bedrag</span>
                        <span className="font-bold text-gray-900">€ 3.000,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eigen risico</span>
                        <span className="font-bold text-gray-900">Geen</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Eenmalige kosten</span>
                        <span className="font-bold text-gray-900">€ 0,00</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Maximale leeftijd</span>
                        <span className="font-bold text-gray-900">N.v.t.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-700">Wachttijd</span>
                        <span className="font-bold text-gray-900">30 dagen</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div className="bg-orange-50 p-6 text-center">
                    <div className="mb-4">
                      <span className="text-gray-700 text-sm">vanaf </span>
                      <span className="text-3xl font-black text-gray-900">€ {currentAnimal.pricing.percentage90.price},</span>
                      <span className="text-xl font-black text-gray-900">{currentAnimal.pricing.percentage90.cents}</span>
                      <span className="text-gray-700 text-sm"> p/m</span>
                    </div>

                    <a
                      href={getFigoAffiliateLink(selectedAnimal)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="btn-cta block text-center font-bold mb-4"
                    >
                      Bekijken &gt;
                    </a>

                    <button
                      onClick={() => document.getElementById('kosten-beperkingen')?.scrollIntoView({ behavior: 'smooth' })}
                      className="text-gray-700 text-sm hover:underline"
                    >
                      Bekijk alle details
                    </button>
                  </div>
                </div>
              </div>

              {/* Compare Button */}
              <div className="text-center">
                <Link
                  href="/"
                  className="btn-cta-outline inline-block font-bold"
                >
                  Vergelijk huisdierverzekeringen
                </Link>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </section>


      {/* Why Choose Figo Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Image
                src="/images/figo/hondenverzekering-2.jpg"
                alt="Hondenverzekering"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-tea_green-100 mb-6">
                Wat maakt Figo de juiste keuze voor jouw huisdier?
              </h2>
              <p className="text-gray-700 mb-6">
                Met wereldwijd meer dan 1,5 miljoen verzekerde huisdieren heeft Figo zich ontwikkeld tot een toonaangevende expert op het gebied van dierenverzekeringen. Figo onderscheidt zich in de Nederlandse markt door naast honden- en kattenverzekeringen ook dekking te bieden voor konijnen en papegaaien. Daarnaast biedt Figo je de vrijheid om zowel je dekkingspercentage als het maximale verzekerde bedrag volledig naar eigen wens samen te stellen. Dit zorgt voor maximale flexibiliteit bij het samenstellen van jouw ideale verzekering.
              </p>
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-tea_green-100">Alle voordelen op een rij</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-tea_green-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Door echte dierenliefhebbers ontwikkeld voor huisdiereigenaren</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-tea_green-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Uitgebreide basisdekking voor optimale bescherming</span>
                  </li>
                  <li className="flex items-center">
                    <svg className="w-5 h-5 text-tea_green-200 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">Geen beperkingen qua ras of leeftijd bij aanmelding</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Figo Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-tea_green-100 mb-8 text-center">
            Kennismaking met Figo als verzekeraar
          </h2>
          <div className="prose prose-lg max-w-none text-gray-700">
            <p>
              Figo opereert als onderdeel van een internationale organisatie met het hoofdkantoor gevestigd in Luxemburg. Het merk is actief in verschillende landen, waaronder België en de Verenigde Staten. Het Nederlandse team bestaat uit gespecialiseerde professionals, waarbij alle klantenservicemedewerkers beschikken over een achtergrond in de diergeneeskunde.
            </p>
            <p>
              Figo&apos;s doelstelling is helder: het bieden van de beste mogelijke zorg voor jouw huisdier. Deze passie voor dieren is merkbaar in alle aspecten van hun service en klantenbehandeling. Door nauwe samenwerking met dierenartsen en fokkers ontwikkelt Figo complete en betrouwbare verzekeringspakketten. Je hebt de keuze uit drie dekkingspercentages: 50%, 70% of 90% vergoeding van je dierenartskosten. Tevens kun je het jaarlijkse maximum kiezen tussen € 3.000, € 6.000 of onbeperkte dekking. Natuurlijk geldt: hoe uitgebreider je dekking, hoe hoger je maandelijkse premie.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-tea_green-100 mb-8 text-center">
            Vraag en antwoord
          </h2>
          <p className="text-gray-700 mb-8 text-center">
            Wij geven antwoord op veelgestelde vragen over de dierenverzekeringen van Figo:
          </p>

          <div className="space-y-6">
            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Wat kost een huisdierenverzekering van Figo?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Figo biedt flexibele tarieven die je volledig kunt aanpassen aan jouw wensen. Voor katten start de basisdekking rond € 11 per maand, terwijl hondenverzekeringen beginnen vanaf ongeveer € 17 maandelijks. Kies je voor de meest complete dekking, dan betaal je ongeveer € 30 per maand voor een kat en rond € 40 voor een hond. Let op: bij grote hondenrassen of rassen met verhoogde gezondheidsrisico&apos;s kunnen de premies hoger uitvallen.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Welke dieren kun je bij Figo verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Figo biedt als enige Nederlandse verzekeraar dekking voor vier verschillende diersoorten: honden, katten, konijnen en papegaaien. Dit maakt Figo uniek in de Nederlandse markt voor huisdiereigenaren met minder gangbare huisdieren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Heeft Figo ook aanvullende verzekeringen?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Ja, Figo biedt twee aanvullende modules waarmee je de basisdekking kunt uitbreiden. Voor een beperkte meerprijs kun je kiezen voor dekking van gebitsbehandelingen of ziektekosten tijdens reizen met je huisdier.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Krijg je korting als je meerdere dieren bij Figo verzekert?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Zeker! Wanneer je meerdere huisdieren verzekert bij Figo, ontvang je vanaf het tweede dier 5% korting op de premie voor al je verzekerde huisdieren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Kun je bij Figo ook oudere dieren verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Absoluut. Figo hanteert geen leeftijdsgrens voor het afsluiten van een verzekering, in tegenstelling tot veel andere verzekeraars. Dit maakt het mogelijk om ook oudere honden en katten nog te verzekeren.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Kun je bij Figo ook andere knaagdieren verzekeren?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>De konijnenverzekering geldt voor alle tamme konijnenrassen en ook voor chinchilla&apos;s. Andere knaagdieren zoals cavia&apos;s of hamsters vallen helaas niet onder de dekking.</p>
              </div>
            </details>

            <details className="bg-white rounded-lg shadow-sm border border-gray-200">
              <summary className="p-6 cursor-pointer font-semibold text-tea_green-100 hover:bg-gray-50">
                Welke vogelsoorten kun je verzekeren bij Figo?
              </summary>
              <div className="px-6 pb-6 text-gray-700">
                <p>Met de papegaaienverzekering kun je verschillende soorten verzekeren, waaronder agapornis, ara&apos;s, grijze roodstaarten, kaketoes en (halsband)parkieten. Ook bepaalde amazonevogels vallen hieronder. Pluimvee, kanaries en duiven zijn uitgesloten van dekking.</p>
              </div>
            </details>
          </div>
        </div>
      </section>

      {/* Reviews Section */}
      <section className="py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-tea_green-100 mb-8">
            Beoordelingen van Figo
          </h2>

          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <div className="text-6xl font-bold text-tea_green-100 mb-4">7.9</div>
            <p className="text-gray-600 mb-6">Op basis van 41 klantbeoordelingen</p>

            <div className="grid md:grid-cols-4 gap-6">
              <div>
                <div className="text-2xl font-bold text-tea_green-100">8.3</div>
                <div className="text-sm text-gray-600">Gemak van afsluiten</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-100">8.1</div>
                <div className="text-sm text-gray-600">Klantenservice</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-100">7.9</div>
                <div className="text-sm text-gray-600">Declaraties</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-tea_green-100">7.2</div>
                <div className="text-sm text-gray-600">Prijs-kwaliteit</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Uitgebreide dekking overzicht - Moved Above Footer */}
      <section id="specifications" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-tea_green-100 text-center mb-12">
            Vergelijk alle specificaties
          </h2>

          {/* Animal Selection Tabs - Styled like Reference Image */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {Object.entries(animalData).map(([key, animal]) => (
              <button
                key={key}
                onClick={() => setSelectedAnimal(key)}
                className={`flex items-center px-6 py-3 rounded-lg transition-all duration-300 ${
                  selectedAnimal === key
                    ? 'bg-tea_green-900 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <div className="w-12 h-12 rounded-lg overflow-hidden mr-3 bg-white">
                  <Image
                    src={animal.tabImage}
                    alt={animal.name}
                    width={48}
                    height={48}
                    className="object-cover w-full h-full"
                  />
                </div>
                <span className="font-semibold">{animal.name}</span>
              </button>
            ))}
          </div>

          {/* Main Pricing Display - Based on Reference Image Layout */}
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedAnimal}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="grid lg:grid-cols-4 gap-8 items-start mb-16"
            >
              {/* Animal Image - Large */}
              <div className="lg:col-span-1">
                <div className="bg-gradient-to-br from-green-100 to-green-200 rounded-2xl overflow-hidden min-h-[300px] flex items-center justify-center">
                  <Image
                    src={currentAnimal.image}
                    alt={currentAnimal.name}
                    width={300}
                    height={300}
                    className="object-cover w-full h-full"
                  />
                </div>
              </div>

              {/* Pricing Cards */}
              <div className="lg:col-span-3 grid md:grid-cols-3 gap-6">
                {/* 50% Card */}
                <div className="bg-gray-50 rounded-2xl p-6 text-center">
                  <div className="text-2xl font-bold text-gray-700 mb-2">50%</div>
                  <div className="text-sm text-gray-600 mb-4">vanaf</div>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">€ {currentAnimal.pricing.percentage50.price},</span>
                    <span className="text-xl font-bold text-gray-900">{currentAnimal.pricing.percentage50.cents}</span>
                  </div>
                  <a
                    href={getFigoAffiliateLink(selectedAnimal)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-cta block text-center"
                  >
                    Bekijken &gt;
                  </a>
                </div>

                {/* 70% Card */}
                <div className="bg-gray-50 rounded-2xl p-6 text-center">
                  <div className="text-2xl font-bold text-gray-700 mb-2">70%</div>
                  <div className="text-sm text-gray-600 mb-4">vanaf</div>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">€ {currentAnimal.pricing.percentage70.price},</span>
                    <span className="text-xl font-bold text-gray-900">{currentAnimal.pricing.percentage70.cents}</span>
                  </div>
                  <a
                    href={getFigoAffiliateLink(selectedAnimal)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-cta block text-center"
                  >
                    Bekijken &gt;
                  </a>
                </div>

                {/* 90% Card */}
                <div className="bg-gray-50 rounded-2xl p-6 text-center">
                  <div className="text-2xl font-bold text-gray-700 mb-2">90%</div>
                  <div className="text-sm text-gray-600 mb-4">vanaf</div>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-gray-900">€ {currentAnimal.pricing.percentage90.price},</span>
                    <span className="text-xl font-bold text-gray-900">{currentAnimal.pricing.percentage90.cents}</span>
                  </div>
                  <a
                    href={getFigoAffiliateLink(selectedAnimal)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-cta block text-center"
                  >
                    Bekijken &gt;
                  </a>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Detailed Specification Tables */}
          <AnimatePresence mode="wait">
            <motion.div
              key={selectedAnimal}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Table 1: Kosten & beperkingen */}
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h3 id="kosten-beperkingen" className="text-xl font-bold text-tea_green-100">Kosten & beperkingen</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="text-left py-4 px-6 text-gray-700 font-bold"></th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">50%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">70%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">90%</th>
                      </tr>
                    </thead>
                    <tbody className="text-sm">
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Max. vergoeding per jaar</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 3.000,00</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 3.000,00</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 3.000,00</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Eigen risico</td>
                        <td className="py-4 px-4 text-center text-gray-900">Geen</td>
                        <td className="py-4 px-4 text-center text-gray-900">Geen</td>
                        <td className="py-4 px-4 text-center text-gray-900">Geen</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Maximale leeftijd bij afsluiten</td>
                        <td className="py-4 px-4 text-center text-gray-900">N.v.t.</td>
                        <td className="py-4 px-4 text-center text-gray-900">N.v.t.</td>
                        <td className="py-4 px-4 text-center text-gray-900">N.v.t.</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Wachttijd</td>
                        <td className="py-4 px-4 text-center text-gray-900">30 dagen</td>
                        <td className="py-4 px-4 text-center text-gray-900">30 dagen</td>
                        <td className="py-4 px-4 text-center text-gray-900">30 dagen</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Eenmalige kosten</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 0,00</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 0,00</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 0,00</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Premiekorting bij verhoogd eigen risico</td>
                        <td className="py-4 px-4 text-center text-gray-900">Ja</td>
                        <td className="py-4 px-4 text-center text-gray-900">Ja</td>
                        <td className="py-4 px-4 text-center text-gray-900">Ja</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="py-4 px-6 text-gray-800 font-bold">Voorwaarden</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-medium">
                          <a href="https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&ued=https%3A%2F%2Ffigopet.nl%2Fklantenservice%2Fdocumenten%2F&clickref=zoekdierenverzekering.nl&clickref2=voorwaarden" target="_blank" rel="noopener noreferrer" className="hover:underline">
                            Polisvoorwaarden
                          </a>
                        </td>
                        <td className="py-4 px-4 text-center text-orange-600 font-medium">
                          <a href="https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&ued=https%3A%2F%2Ffigopet.nl%2Fklantenservice%2Fdocumenten%2F&clickref=zoekdierenverzekering.nl&clickref2=voorwaarden" target="_blank" rel="noopener noreferrer" className="hover:underline">
                            Polisvoorwaarden
                          </a>
                        </td>
                        <td className="py-4 px-4 text-center text-orange-600 font-medium">
                          <a href="https://www.awin1.com/cread.php?awinmid=24319&awinaffid=2107735&ued=https%3A%2F%2Ffigopet.nl%2Fklantenservice%2Fdocumenten%2F&clickref=zoekdierenverzekering.nl&clickref2=voorwaarden" target="_blank" rel="noopener noreferrer" className="hover:underline">
                            Polisvoorwaarden
                          </a>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Table 2: Dekking algemeen */}
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h3 className="text-xl font-bold text-tea_green-100">Dekking algemeen</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="text-left py-4 px-6 text-gray-700 font-bold">Behandeling</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">50%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">70%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">90%</th>
                      </tr>
                    </thead>
                    <tbody className="text-sm">
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Castratie / sterilisatie</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Consult dierenarts</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Dekking buitenland</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Diergeneesmiddelen</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Identificatiechip</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Operaties</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Opname / verblijf kliniek</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Vaccinaties</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Table 3: Overige vergoedingen */}
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h3 className="text-xl font-bold text-tea_green-100">Overige vergoedingen</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="text-left py-4 px-6 text-gray-700 font-bold">Behandeling</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">50%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">70%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">90%</th>
                      </tr>
                    </thead>
                    <tbody className="text-sm">
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Aandoening gewrichten</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Advertentie bij vermissing</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Alternatieve geneeswijzen</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Bloedonderzoek</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Crematie</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Dieetvoeding en supplementen</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                        <td className="py-4 px-4 text-center text-red-600 font-bold">0%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Dierenambulance</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Echo</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Euthanasie</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Fysio- of hydrotherapie</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Gedragstherapie</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Huidallergieën</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Hulpmiddelen</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Laboratorisch onderzoek</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">MRI of CT-scan</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Noodopvang huisdier</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Ongevallen</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Röntgenonderzoek</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Tandzorg</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                        <td className="py-4 px-4 text-center text-orange-600 font-bold">0%*</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Verwijderen wolfs- / hubertusklauw</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 50%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 70%</td>
                        <td className="py-4 px-4 text-center text-green-600 font-bold">✓ 90%</td>
                      </tr>
                    </tbody>
                  </table>
                  <div className="px-6 py-4 bg-gray-50 text-xs text-gray-600">
                    * Extra verzekeren is mogelijk
                  </div>
                </div>
              </div>

              {/* Table 4: Aanvullende verzekeringen */}
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <h3 className="text-xl font-bold text-tea_green-100">Aanvullende verzekeringen</h3>
                </div>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="text-left py-4 px-6 text-gray-700 font-bold">Aanvulling</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">50%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">70%</th>
                        <th className="text-center py-4 px-4 text-gray-700 font-bold">90%</th>
                      </tr>
                    </thead>
                    <tbody className="text-sm">
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Gebit</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 2,78</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 4,03</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 5,58</td>
                      </tr>
                      <tr className="border-b border-gray-100">
                        <td className="py-4 px-6 text-gray-700 font-medium">Reisverzekering</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 2,52</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 3,23</td>
                        <td className="py-4 px-4 text-center text-gray-900">€ 3,96</td>
                      </tr>
                    </tbody>
                  </table>
                  <div className="px-6 py-4 bg-gray-50 text-xs text-gray-600">
                    Alle tarieven inclusief assurantiebelasting. Bij sommige vergoedingen kan een maximum uitkering van toepassing zijn.
                  </div>
                </div>
              </div>

            </motion.div>
          </AnimatePresence>
        </div>
      </section>

      {/* Back to Comparison */}
      <section className="py-16 bg-tea_green-100">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Wil je meer verzekeraars vergelijken?
          </h2>
          <p className="text-xl text-white mb-8">
            Ga terug naar onze vergelijker om alle Nederlandse dierenverzekeraars te bekijken.
          </p>
          <Link
            href="/"
            className="inline-block bg-white text-tea_green-100 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors"
          >
            Terug naar vergelijker
          </Link>
        </div>
      </section>

      {/* Footer - Match Homepage Style */}
      <Footer />
    </div>
  )
}
