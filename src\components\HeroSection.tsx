"use client"

import Navigation from "./Navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Shield, Users, CheckCircle, ArrowRight } from "lucide-react";

export default function HeroSection() {
  return (
    <section className="relative bg-gradient-to-br from-tea_green-900 via-tea_green-800 to-tea_green-900 overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-celeste-400/5 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-celadon-400/5 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-celeste-400/3 rounded-full blur-3xl"></div>
      </div>

      {/* Navigation */}
      <Navigation />
      
      {/* Main content */}
      <div className="relative pt-24 pb-16 sm:pt-32 sm:pb-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-8">
          <div className="grid lg:grid-cols-12 gap-8 lg:gap-12 items-center">
            
            {/* Left content - 7 columns */}
            <motion.div
              className="lg:col-span-7 space-y-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              {/* Trust badges */}
              <motion.div
                className="flex flex-wrap gap-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1, ease: "easeOut" }}
              >
                <Badge className="bg-celadon-400/20 text-celadon-100 border-celadon-400/30 hover:bg-celadon-400/30">
                  <Shield className="w-3 h-3 mr-1" />
                  Vergelijkingsplatform
                </Badge>
                <Badge className="bg-celeste-400/20 text-celeste-100 border-celeste-400/30 hover:bg-celeste-400/30">
                  <Users className="w-3 h-3 mr-1" />
                  Nederlandse Verzekeraars
                </Badge>
              </motion.div>

              {/* Headlines */}
              <div className="space-y-4">
                <motion.h1
                  className="text-4xl sm:text-5xl lg:text-6xl font-bold text-celadon-100 leading-tight tracking-tight"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
                >
                  Vergelijk Alle Dierenverzekeringen
                  <span className="block text-celeste-400">Nederland</span>
                </motion.h1>
                <motion.p
                  className="text-xl text-celadon-300 max-w-2xl leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                >
                  Vergelijk Nederlandse dierenverzekeringen en vind de beste dekking voor jouw huisdier. 
                  We vergelijken meerdere aanbieders (momenteel Figo en OHRA).
                </motion.p>
              </div>

              {/* Benefits list */}
              <motion.div
                className="grid grid-cols-1 sm:grid-cols-2 gap-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
              >
                {[].map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-celeste-400 flex-shrink-0" />
                    <span className="text-celadon-200">{benefit}</span>
                  </div>
                ))}
              </motion.div>

              {/* CTAs */}
              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
              >
                <Button 
                  size="lg" 
                  className="bg-white text-tea_green-900 hover:bg-gray-50 px-8 py-4 text-lg font-bold shadow-lg hover:shadow-xl transition-all group border-2 border-white"
                  onClick={() => {
                    const element = document.querySelector('[data-section="best-beoordeeld"]');
                    element?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  Vergelijk Nu Gratis
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </motion.div>
            </motion.div>

            {/* Right visual - 5 columns */}
            <motion.div
              className="lg:col-span-5 relative"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
            >
              {/* Pet showcase */}
              <div className="relative mx-auto w-fit">
                <div className="flex items-center justify-center mb-6">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 1, ease: "easeOut" }}
                    className="relative"
                  >
                    <Avatar className="w-64 h-64 border-4 border-celadon-400 shadow-xl">
                      <AvatarImage src="/images/hero.webp" alt="Dierenverzekering Nederland - Hond en kat verzekeren" />
                      <AvatarFallback className="bg-celadon-100 text-tea_green-900 text-6xl">🐾</AvatarFallback>
                    </Avatar>
                    {/* Floating heart */}
                    <motion.div
                      className="absolute -top-2 -right-2 w-10 h-10 bg-red-500 rounded-full flex items-center justify-center shadow-lg"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 1.8, ease: "backOut" }}
                    >
                      <span className="text-white text-lg">❤️</span>
                    </motion.div>
                  </motion.div>
                </div>

              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}