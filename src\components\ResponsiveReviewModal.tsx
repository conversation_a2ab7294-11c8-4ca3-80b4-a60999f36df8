"use client"

import { useState, useEffect, useCallback } from "react"
import { motion } from "framer-motion"
import { X } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Drawer, Drawer<PERSON>ontent, Draw<PERSON><PERSON>eader, DrawerTitle } from "@/components/ui/drawer"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

interface Review {
  id: string
  name: string
  rating: number
  comment: string
  date: string
  categories: {
    gemakVanAfsluiten: number
    klantenservice: number
    declaraties: number
    prijsKwaliteit: number
  }
}

interface ResponsiveReviewModalProps {
  isOpen: boolean
  onClose: () => void
  companyName: string
  overallRating: number
  totalReviews: number
}

// Sample review data - same as original
const figoReviews: Review[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON>",
    rating: 9,
    comment: "Prima dierenverzekering! Declaraties zijn makkelijk in te dienen via de app en snelle uitbetaling binnen 5 werkdagen. Goede dekking voor mijn hond.",
    date: "1 week geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 9 }
  },
  {
    id: "2",
    name: "Almar Jansen",
    rating: 8,
    comment: "Uitstekende klantenservice en vriendelijke medewerkers. Makkelijke claims afhandeling. Alleen jammer dat preventieve screeningen niet volledig vergoed worden.",
    date: "2 weken geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 9, declaraties: 8, prijsKwaliteit: 8 }
  },
  {
    id: "3",
    name: "Sandra Bakker",
    rating: 9,
    comment: "Zeer tevreden met Figo! Snelle vergoeding van dierenarts kosten en uitgebreide dekking. Mijn kat is goed verzekerd tegen hoge veterinaire kosten.",
    date: "3 weken geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 8, declaraties: 9, prijsKwaliteit: 9 }
  },
  {
    id: "4",
    name: "Peter de Vries",
    rating: 8,
    comment: "Goede prijs-kwaliteit verhouding voor huisdierenverzekering. Claims worden snel behandeld en de online portal is gebruiksvriendelijk.",
    date: "1 maand geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 9 }
  },
  {
    id: "5",
    name: "Linda Smit",
    rating: 9,
    comment: "Fantastische ervaring met Figo! Goede dekking voor operaties en behandelingen. Vriendelijke klantenservice die altijd bereikbaar is.",
    date: "1 maand geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 8, prijsKwaliteit: 8 }
  },
  {
    id: "6",
    name: "Marco van Dijk",
    rating: 8,
    comment: "Betrouwbare dierenverzekering met snelle uitbetaling. Mijn hond kreeg een operatie en alles werd keurig vergoed volgens de voorwaarden.",
    date: "6 weken geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "7",
    name: "Evelien Mulder",
    rating: 9,
    comment: "Top verzekering! Makkelijke online aanvraag en goede communicatie. Claims worden professioneel afgehandeld door deskundige medewerkers.",
    date: "2 maanden geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "8",
    name: "Robert Hendriks",
    rating: 8,
    comment: "Solide keuze voor huisdierenverzekering. Goede dekking tegen redelijke premie. Declaratie proces is eenvoudig en transparant.",
    date: "2 maanden geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 9 }
  },
  {
    id: "9",
    name: "Marieke Visser",
    rating: 9,
    comment: "Zeer positieve ervaring! Snelle afhandeling van claims en uitstekende klantenservice. Mijn kat is optimaal verzekerd tegen ziekte en ongevallen.",
    date: "3 maanden geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "10",
    name: "Tom van Leeuwen",
    rating: 8,
    comment: "Goede dierenverzekering met faire voorwaarden. Claims worden correct uitgekeerd en de premie is betaalbaar voor de geboden dekking.",
    date: "3 maanden geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 8 }
  }
]

const ohraReviews: Review[] = [
  {
    id: "1",
    name: "Maria van den Berg",
    rating: 8,
    comment: "Zeer tevreden met OHRA dierenverzekering. Snelle afhandeling van claims en uitstekende klantenservice. Goede dekking voor mijn hond tegen redelijke premie.",
    date: "1 week geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 9, declaraties: 8, prijsKwaliteit: 8 }
  },
  {
    id: "2",
    name: "Jan Pietersen",
    rating: 9,
    comment: "Uitstekende huisdierenverzekering met hoge vergoedingspercentages van 80%. Snelle uitbetaling en vriendelijke klantenservice. Absolute aanrader!",
    date: "2 weken geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 8, declaraties: 9, prijsKwaliteit: 9 }
  },
  {
    id: "3",
    name: "Karin Smits",
    rating: 9,
    comment: "Prima ervaring met OHRA! Makkelijke claims via de app en snelle vergoeding van dierenarts kosten. Mijn kat is goed verzekerd tegen ziekte en ongevallen.",
    date: "3 weken geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "4",
    name: "Henk van der Meer",
    rating: 8,
    comment: "Betrouwbare verzekeraar met goede dekking. Claims worden professioneel behandeld en uitbetaling verloopt vlot. Goede prijs-kwaliteit verhouding.",
    date: "1 maand geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 9 }
  },
  {
    id: "5",
    name: "Annemarie Jansen",
    rating: 9,
    comment: "Fantastische dierenverzekering! Uitgebreide dekking en snelle afhandeling van declaraties. Klantenservice is zeer behulpzaam en deskundig.",
    date: "1 maand geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 8, prijsKwaliteit: 8 }
  },
  {
    id: "6",
    name: "Paul Bakker",
    rating: 8,
    comment: "Goede ervaring met OHRA huisdierenverzekering. Mijn hond had een operatie nodig en alles werd snel en correct vergoed. Transparante voorwaarden.",
    date: "6 weken geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "7",
    name: "Ingrid de Wit",
    rating: 9,
    comment: "Top service van OHRA! Makkelijke online aanvraag en vriendelijke medewerkers. Claims worden snel behandeld en uitbetaald binnen een week.",
    date: "2 maanden geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "8",
    name: "Gerard Mulder",
    rating: 8,
    comment: "Solide keuze voor dierenverzekering. Goede dekking tegen betaalbare premie. Declaratie proces is eenvoudig via de online portal.",
    date: "2 maanden geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 9 }
  },
  {
    id: "9",
    name: "Sylvia van Dijk",
    rating: 9,
    comment: "Zeer tevreden klant! Snelle afhandeling van claims en uitstekende communicatie. Mijn kat is optimaal verzekerd tegen hoge veterinaire kosten.",
    date: "3 maanden geleden",
    categories: { gemakVanAfsluiten: 9, klantenservice: 9, declaraties: 9, prijsKwaliteit: 8 }
  },
  {
    id: "10",
    name: "Frank Hendriks",
    rating: 8,
    comment: "Betrouwbare dierenverzekering met faire voorwaarden. Claims worden correct afgehandeld en de klantenservice is altijd bereikbaar voor vragen.",
    date: "3 maanden geleden",
    categories: { gemakVanAfsluiten: 8, klantenservice: 8, declaraties: 8, prijsKwaliteit: 8 }
  }
]

export default function ResponsiveReviewModal({
  isOpen,
  onClose,
  companyName,
  overallRating,
  totalReviews
}: ResponsiveReviewModalProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [activeTab, setActiveTab] = useState<'reviews' | 'write'>('reviews')
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    overallRating: 0,
    gemakVanAfsluiten: 0,
    klantenservice: 0,
    declaraties: 0,
    prijsKwaliteit: 0,
    comment: ''
  })

  useEffect(() => {
    setIsClient(true)
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Memoized form update functions to prevent input focus loss
  const updateName = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, name: value }))
  }, [])

  const updateEmail = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, email: value }))
  }, [])

  const updateComment = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, comment: value }))
  }, [])

  const updateOverallRating = useCallback((value: number) => {
    setFormData(prev => ({ ...prev, overallRating: value }))
  }, [])

  const updateGemakVanAfsluiten = useCallback((value: number) => {
    setFormData(prev => ({ ...prev, gemakVanAfsluiten: value }))
  }, [])

  const updateKlantenservice = useCallback((value: number) => {
    setFormData(prev => ({ ...prev, klantenservice: value }))
  }, [])

  const updateDeclaraties = useCallback((value: number) => {
    setFormData(prev => ({ ...prev, declaraties: value }))
  }, [])

  const updatePrijsKwaliteit = useCallback((value: number) => {
    setFormData(prev => ({ ...prev, prijsKwaliteit: value }))
  }, [])

  const reviews = companyName === 'Figo' ? figoReviews : ohraReviews
  
  const averageCategories = {
    gemakVanAfsluiten: Math.round(reviews.reduce((sum, review) => sum + review.categories.gemakVanAfsluiten, 0) / reviews.length * 10) / 10,
    klantenservice: Math.round(reviews.reduce((sum, review) => sum + review.categories.klantenservice, 0) / reviews.length * 10) / 10,
    declaraties: Math.round(reviews.reduce((sum, review) => sum + review.categories.declaraties, 0) / reviews.length * 10) / 10,
    prijsKwaliteit: Math.round(reviews.reduce((sum, review) => sum + review.categories.prijsKwaliteit, 0) / reviews.length * 10) / 10
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-lg ${i < rating ? 'text-green-500' : 'text-gray-300'}`}>
        ★
      </span>
    ))
  }

  const handleFormSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    // Simulate form submission
    setShowSuccessMessage(true)
    setTimeout(() => {
      setShowSuccessMessage(false)
      setActiveTab('reviews')
      setFormData({
        name: '',
        email: '',
        overallRating: 0,
        gemakVanAfsluiten: 0,
        klantenservice: 0,
        declaraties: 0,
        prijsKwaliteit: 0,
        comment: ''
      })
    }, 6000)
  }, [])

  const renderRatingInput = (label: string, value: number, onChange: (rating: number) => void) => {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="flex gap-1">
          {Array.from({ length: 10 }, (_, i) => (
            <button
              key={i}
              type="button"
              onClick={() => onChange(i + 1)}
              className={`w-8 h-8 rounded-full text-sm font-bold transition-colors ${
                i < value
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              {i + 1}
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Main content component that will be used in both Dialog and Drawer
  const ModalContent = () => (
    <div className="flex flex-col h-full">
      {/* Header with rating card - matches Figma design */}
      <div className="bg-[#2F2E51] text-white p-4 md:p-6 rounded-t-2xl md:rounded-t-lg">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl md:text-2xl font-bold">Beoordelingen van {companyName}</h2>
          {!isMobile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-white hover:text-gray-300 hover:bg-white/10 h-11 w-11"
            >
              <X className="h-6 w-6" />
              <span className="sr-only">Sluiten</span>
            </Button>
          )}
        </div>
        
        {/* Rating Card - matches the Figma design */}
        <div className="bg-[#2F2E51] rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between text-white mb-4">
            <div className="flex items-center">
              <span className="text-4xl md:text-5xl font-bold text-green-400 mr-4">{overallRating}</span>
              <div>
                <div className="text-sm opacity-90">Op basis van {totalReviews}</div>
                <div className="text-sm opacity-90">klantbeoordelingen</div>
              </div>
            </div>
          </div>
          
          {/* Category ratings */}
          <div className="grid grid-cols-2 gap-3 md:gap-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-white/90">Gemak van afsluiten</span>
              <span className="font-bold text-green-400">{averageCategories.gemakVanAfsluiten}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-white/90">Klantenservice</span>
              <span className="font-bold text-green-400">{averageCategories.klantenservice}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-white/90">Declaraties</span>
              <span className="font-bold text-green-400">{averageCategories.declaraties}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-white/90">Prijs-kwaliteit</span>
              <span className="font-bold text-green-400">{averageCategories.prijsKwaliteit}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'reviews' | 'write')} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mx-4 md:mx-6 mt-4">
          <TabsTrigger value="reviews" className="h-11">Beoordelingen</TabsTrigger>
          <TabsTrigger value="write" className="h-11">Schrijf een beoordeling</TabsTrigger>
        </TabsList>

        <TabsContent value="reviews" className="flex-1 mt-4">
          <ScrollArea className="h-[400px] md:h-[500px] px-4 md:px-6">
            <div className="space-y-6">
              {reviews.map((review) => (
                <motion.div
                  key={review.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border-b border-gray-200 pb-6 last:border-b-0"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-[#2F2E51] text-lg">{review.name}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        {renderStars(review.rating)}
                        <span className="font-bold text-lg text-green-500">{review.rating}.0</span>
                      </div>
                    </div>
                    <span className="text-sm text-gray-500">{review.date}</span>
                  </div>
                  
                  <p className="text-gray-700 mb-4 leading-relaxed">{review.comment}</p>
                  
                  {/* Category ratings for individual review */}
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Gemak van afsluiten</span>
                      <span className="font-medium text-green-600">{review.categories.gemakVanAfsluiten}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Klantenservice</span>
                      <span className="font-medium text-green-600">{review.categories.klantenservice}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Declaraties</span>
                      <span className="font-medium text-green-600">{review.categories.declaraties}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Prijs-kwaliteit</span>
                      <span className="font-medium text-green-600">{review.categories.prijsKwaliteit}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="write" className="flex-1 mt-4">
          <ScrollArea className="h-[400px] md:h-[500px] px-4 md:px-6">
            <div className="space-y-6">
              {showSuccessMessage ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center py-8"
                >
                  <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <strong>Bedankt! Uw beoordeling is succesvol ingediend en wordt binnenkort gecontroleerd.</strong>
                  </div>
                  <p className="text-gray-600">Uw ervaring helpt andere huisdiereigenaren bij het maken van de juiste keuze.</p>
                </motion.div>
              ) : (
                <form onSubmit={handleFormSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Naam *
                      </label>
                      <Input
                        required
                        type="text"
                        value={formData.name}
                        onChange={(e) => updateName(e.target.value)}
                        className="w-full h-11"
                        placeholder="Uw naam"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        E-mail *
                      </label>
                      <Input
                        required
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateEmail(e.target.value)}
                        className="w-full h-11"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  {renderRatingInput(
                    "Algemene beoordeling *",
                    formData.overallRating,
                    updateOverallRating
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {renderRatingInput(
                      "Gemak van afsluiten",
                      formData.gemakVanAfsluiten,
                      updateGemakVanAfsluiten
                    )}
                    {renderRatingInput(
                      "Klantenservice",
                      formData.klantenservice,
                      updateKlantenservice
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {renderRatingInput(
                      "Declaraties",
                      formData.declaraties,
                      updateDeclaraties
                    )}
                    {renderRatingInput(
                      "Prijs-kwaliteit",
                      formData.prijsKwaliteit,
                      updatePrijsKwaliteit
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Uw ervaring *
                    </label>
                    <Textarea
                      required
                      rows={4}
                      value={formData.comment}
                      onChange={(e) => updateComment(e.target.value)}
                      className="w-full"
                      placeholder="Vertel over uw ervaring met deze verzekeraar..."
                    />
                  </div>

                  <div className="text-center">
                    <Button
                      type="submit"
                      className="bg-[#4DC166] hover:bg-[#45b05c] text-white h-11 px-8"
                    >
                      Verstuur beoordeling
                    </Button>
                  </div>
                </form>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )

  // Prevent hydration mismatch by not rendering until client-side
  if (!isClient) {
    return null
  }

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={onClose}>
        <DrawerContent className="max-h-[90vh]">
          <DrawerHeader className="sr-only">
            <DrawerTitle>Beoordelingen van {companyName}</DrawerTitle>
          </DrawerHeader>
          <ModalContent />
        </DrawerContent>
      </Drawer>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-full max-h-[90vh] p-0 overflow-hidden">
        <DialogHeader className="sr-only">
          <DialogTitle>Beoordelingen van {companyName}</DialogTitle>
        </DialogHeader>
        <ModalContent />
      </DialogContent>
    </Dialog>
  )
}
