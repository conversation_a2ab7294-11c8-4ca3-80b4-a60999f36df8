"use client"

import { motion } from 'framer-motion'

export default function BekenVanSection() {
  // Media logos temporarily removed for Google Ads compliance
  // Only include logos if you have verifiable links to articles/interviews
  
  return (
    <section className="w-full bg-white py-16">
      <div className="max-w-[1284px] mx-auto px-4">
        <motion.h2
          className="text-[42px] font-bold text-[#2F2E51] text-center leading-[1.2] mb-16 font-figtree"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true, margin: "-100px" }}
        >
          Vergelijk Nederlandse Dierenverzekeraars
        </motion.h2>

        <div className="flex justify-center items-center">
          <motion.div
            className="bg-gradient-to-br from-tea_green-100 to-celadon-100 rounded-2xl p-8 max-w-3xl text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <p className="text-lg text-tea_green-900 font-medium mb-4">
              Transparante vergelijking van Nederlandse dierenverzekeringen
            </p>
            <p className="text-tea_green-900 font-medium">
              We helpen huisdiereigenaren bij het maken van weloverwogen keuzes door 
              duidelijke informatie over dekkingen, premies en voorwaarden.
            </p>
          </motion.div>
        </div>
      </div>
    </section>
  )
}