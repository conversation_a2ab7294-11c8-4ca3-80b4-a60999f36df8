import { notFound } from 'next/navigation';
import { getArticleBySlug, artikelsArticles } from '../../../data/artikelsArticles';
import { getMDXContent, extractTableOfContents } from '../../../lib/mdx';
import { calculateReadingTime, formatDutchDate } from '../../../lib/articleUtils';
import Navigation from '../../../components/Navigation';
import Footer from '../../../components/Footer';
import MDXRenderer from '../../../components/MDXRenderer';
import TableOfContents from '../../../components/TableOfContents';
import Image from 'next/image';
import Link from 'next/link';

interface ArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateStaticParams() {
  return artikelsArticles.map((article) => ({
    slug: article.slug,
  }));
}

export async function generateMetadata({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = getArticleBySlug(slug);
  
  if (!article) {
    return {
      title: 'Artikel niet gevonden',
      description: 'Het opgevraagde artikel kon niet worden gevonden.',
    };
  }

  return {
    title: article.metaTitle,
    description: article.metaDescription,
    keywords: article.keywords.join(', '),
    alternates: {
      canonical: `https://zoekdierenverzekering.nl/artikels/${slug}`,
    },
    openGraph: {
      title: article.metaTitle,
      description: article.metaDescription,
      url: `https://zoekdierenverzekering.nl/artikels/${slug}`,
      siteName: "ZoekDierenverzekering.nl",
      type: 'article',
      images: article.image ? [{ url: article.image, width: 1200, height: 630, alt: article.title }] : [],
      locale: "nl_NL",
    },
    twitter: {
      card: "summary_large_image",
      title: article.metaTitle,
      description: article.metaDescription,
      images: article.image ? [article.image] : [],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = getArticleBySlug(slug);

  if (!article) {
    notFound();
  }

  // Load MDX content
  const mdxContent = await getMDXContent(slug);
  const tableOfContents = mdxContent ? extractTableOfContents(mdxContent.content) : [];
  
  // Calculate reading time from content
  const readingTime = mdxContent ? calculateReadingTime(mdxContent.content) : 5;
  
  // Format dates
  const formattedUpdatedDate = formatDutchDate(article.updatedDate);

  return (
    <div className="min-h-screen bg-white">
      {/* Article Schema JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": article.title,
            "description": article.description,
            "image": article.image ? article.image : "https://zoekdierenverzekering.nl/images/hero.webp",
            "author": {
              "@type": "Organization",
              "name": "ZoekDierenverzekering.nl",
              "url": "https://zoekdierenverzekering.nl"
            },
            "publisher": {
              "@type": "Organization",
              "name": "ZoekDierenverzekering.nl",
              "logo": {
                "@type": "ImageObject",
                "url": "https://zoekdierenverzekering.nl/images/logo.webp"
              }
            },
            "datePublished": article.publishedDate,
            "dateModified": article.updatedDate,
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": `https://zoekdierenverzekering.nl/artikels/${slug}`
            },
            "articleSection": article.category,
            "keywords": article.keywords.join(", "),
            "inLanguage": "nl-NL",
            "about": {
              "@type": "Thing",
              "name": "Dierenverzekering",
              "description": "Informatie over dierenverzekeringen en huisdierenzorg"
            }
          })
        }}
      />
      
      {/* Breadcrumb Schema JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://zoekdierenverzekering.nl"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Artikels",
                "item": "https://zoekdierenverzekering.nl/artikels"
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": article.title,
                "item": `https://zoekdierenverzekering.nl/artikels/${slug}`
              }
            ]
          })
        }}
      />

      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-6 bg-[#FFF5ED]">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <Link href="/" className="hover:text-[#2F2E51] transition-colors">
              Home
            </Link>
            <span>›</span>
            <Link href="/artikels" className="hover:text-[#2F2E51] transition-colors">
              Artikels
            </Link>
            <span>›</span>
            <span className="text-[#2F2E51] font-medium">{article.category}</span>
          </nav>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-16 bg-[#FFF5ED]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 xl:gap-20 items-start lg:items-center">
            <div className="lg:pr-8 xl:pr-12">
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                {article.category}
              </div>
              <h1 className="text-4xl sm:text-5xl lg:text-5xl xl:text-6xl font-bold text-[#2F2E51] mb-6 leading-tight">
                {article.title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8 max-w-lg">
                {article.description}
              </p>

              {/* Article Meta */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{readingTime} min leestijd</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span>Laatst bijgewerkt: {formattedUpdatedDate}</span>
                </div>
              </div>
            </div>

            {/* Article Image */}
            <div className="lg:pl-8 xl:pl-12">
              {article.image && (
                <div className="relative w-full h-80 lg:h-96 xl:h-[28rem] rounded-2xl overflow-hidden shadow-xl">
                  <Image
                    src={article.image}
                    alt={article.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-24 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Table of Contents */}
          <TableOfContents items={tableOfContents} />

          {/* Article Content */}
          {mdxContent ? (
            <MDXRenderer content={mdxContent.content} />
          ) : (
            <div className="prose prose-lg max-w-none">
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 mb-8">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      <strong>Let op:</strong> De inhoud voor dit artikel is nog niet beschikbaar. Probeer het later opnieuw.
                    </p>
                  </div>
                </div>
              </div>

              {/* Fallback content */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-[#2F2E51] mb-6 border-b-2 border-orange-200 pb-3">Over dit onderwerp</h2>
                  <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                    {article.description}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-[#2F2E51] text-center mb-12">
            Gerelateerde artikelen
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {artikelsArticles
              .filter(a => a.category === article.category && a.id !== article.id)
              .slice(0, 3)
              .map((relatedArticle) => (
                <Link
                  key={relatedArticle.id}
                  href={`/artikels/${relatedArticle.slug}`}
                  className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                >
                  {relatedArticle.image && (
                    <div className="relative w-full h-48">
                      <Image
                        src={relatedArticle.image}
                        alt={relatedArticle.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <div className="text-sm text-blue-600 font-medium mb-2">
                      {relatedArticle.category}
                    </div>
                    <h3 className="text-lg font-bold text-[#2F2E51] mb-2 hover:text-blue-600 transition-colors">
                      {relatedArticle.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {relatedArticle.description}
                    </p>
                  </div>
                </Link>
              ))}
          </div>
          
          <div className="text-center mt-12">
            <Link
              href="/artikels"
              className="inline-flex items-center gap-2 bg-[#2F2E51] text-white px-8 py-4 rounded-full font-semibold hover:bg-slate-700 transition-all duration-300 hover:scale-105"
            >
              Terug naar Artikels
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
